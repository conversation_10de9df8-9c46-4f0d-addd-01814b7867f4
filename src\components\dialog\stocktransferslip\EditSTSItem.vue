<template>
  <q-dialog v-model="isOpen">
    <q-card style="max-width: 1100px; width: 800px">
      <q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">รายละเอียดสินค้า</div>
          <q-btn icon="close" @click="closeDialog" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section style="overflow: auto; max-height: 80vh">
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            รายละเอียดการสั่งสินค้า
          </div>
          <div class="shadow-2 container">
            <div class="q-mb-md" style="font-size: 16px">
              <!-- รหัสสินค้า และ ชื่อสินค้า -->
              <div class="row items-center q-mt-md">
                <div class="col-12 col-md-6 row items-center">
                  <div class="col-5 text-center">รหัสสินค้า :</div>
                  <div class="col-7">{{ stockStore.formforGR.product.product_code }}</div>
                </div>
                <div class="col-12 col-md-6 row items-center q-mt-xs q-mt-md-none">
                  <div class="col-5 text-center">ชื่อสินค้า :</div>
                  <div class="col-7">{{ stockStore.formforGR.product.product_name }}</div>
                </div>
              </div>
              <div class="row items-center q-mt-md">
                <!-- สถานที่เก็บ -->
                <div class="col-12 col-md-6 row items-center">
                  <div class="col-5 text-center">สถานที่เก็บ :</div>
                  <div class="col-7">
                    {{ stockStore.formforGR.product.storage_location }}
                  </div>
                </div>

                <!-- จำนวนคงเหลือ + ปุ่มในแนวเดียวกัน -->
                <div class="col-12 col-md-6 row items-center q-mt-xs q-mt-md-none">
                  <div class="col-5 text-center">จำนวนคงเหลือ :</div>
                  <div class="col-7 row items-center no-wrap">
                    <div>
                      {{ stockStore.formforGR.remaining }}
                      <span>{{ stockStore.formforGR.product.unit }}</span>
                    </div>
                    <q-btn
                      label="ดูจำนวนทุกสาขา"
                      flat
                      size="sm"
                      class="btn-add q-ml-sm"
                      @click="viewStockByBranchDialogOpen = true"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- รายการสินค้า -->
        <div class="gap-container" style="margin-top: 20px">
          <div class="text-white shadow-2 container-header row items-center">รายการสินค้า</div>
          <div class="shadow-2">
            <q-table
              flat
              class="body-table"
              :rows="store.sto"
              :columns="columns"
              row-key="id"
              :pagination="pagination"
              :rows-per-page-options="[]"
            >
              <!-- ช่อง input จำนวนสั่ง -->
              <template v-slot:body-cell-orderQuantity="props">
                <q-td :props="props" class="text-right">
                  <q-input
                    v-model.number="props.row.quantity"
                    dense
                    type="number"
                    class="input-container"
                    style="max-width: 100px"
                  />
                </q-td>
              </template>

              <!-- แถวรวม -->
              <template v-slot:bottom-row>
                <q-tr class="total-row">
                  <q-td colspan="5" class="text-right total-label">รวม</q-td>
                  <q-td class="text-right total-value">
                    <div class="row justify-end items-center no-wrap">
                      <!-- <span>{{ totalAmount }}</span> -->
                    </div>
                  </q-td>
                </q-tr>
              </template>
            </q-table>
          </div>
        </div>
      </q-card-section>
      <q-card-actions align="center" style="color: white">
        <q-btn class="btn-accept" dense flat label="บันทึก" @click="saveDialog" />
        <q-btn class="btn-cancel" dense flat label="ยกเลิก" @click="closeDialog" />
      </q-card-actions>
    </q-card>
  </q-dialog>
  <ViewStockByBranch v-model="viewStockByBranchDialogOpen"></ViewStockByBranch>
</template>

<script setup lang="ts">
import { useStockStore } from 'src/stores/stock'
import { useStockTransferOrderStore } from 'src/stores/stocktransferorder'
import type { StockTransferOrderDetails } from 'src/types/stockTransferOrderDetails'
import { computed, ref, watch } from 'vue'
// import ViewStockByBranch from './ViewStockByBranch.vue'
import { QInput } from 'quasar'
import type { QTableColumn } from 'quasar'

const store = useStockTransferOrderStore()

// Props and emits for v-model support
interface Props {
  modelValue: boolean
  stoItem?: StockTransferOrderDetails | null
  mode?: string
}

const props = withDefaults(defineProps<Props>(), {
  stoItem: null,
  mode: 'add',
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'item-updated': []
}>()

// Computed property for v-model
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const stockStore = useStockStore()
const stoStore = useStockTransferOrderStore()
const viewStockByBranchDialogOpen = ref(false)
const quantityInput = ref<InstanceType<typeof QInput> | null>(null)
watch(
  () => props.modelValue,
  () => {
    const inputEl = quantityInput.value?.getNativeElement?.()
    if (inputEl) {
      inputEl.focus()
      inputEl.select()
    }
  },
)

const saveDialog = () => {
  const index = stoStore.stoDetails.findIndex(
    (item) => item.product.id === stoStore.formSTODetails.product.id,
  )

  if (props.mode === 'edit') {
    if (index !== -1) {
      stoStore.stoDetails[index] = {
        ...stoStore.stoDetails[index],
        ...stoStore.formSTODetails,
      }
    } else {
      stoStore.stoDetails.push(stoStore.formSTODetails)
    }
    stoStore.deletedIds = stoStore.deletedIds.filter(
      (id) => id !== stoStore.formSTODetails.product.id,
    )
  } else {
    const exists = stoStore.stoDetailsEdited.some(
      (item) => item.product.id === stoStore.formSTODetails.product.id,
    )
    if (!exists) {
      stoStore.stoDetailsEdited.push(stoStore.formSTODetails)
    }
    // else{
    //   store.grDetailsEdited[index] = {
    //     ...originalItem,
    //     ...copiedFormOrderItems,
    //     id: oldId, // ป้องกัน id หาย
    //     quantity: updatedQuantity
    //   };
    // }
  }

  isOpen.value = false
  stoStore.resetFormSTODetails()
}

const pagination = ref({
  rowsPerPage: 12,
})

const closeDialog = () => {
  if (props.stoItem) {
    // หา item ที่เพิ่งบันทึก แล้วอัปเดตกลับเข้า formOrderItems
    const editedItem = stoStore.stoDetails.find(
      (item) => item.product.id === stoStore.formSTODetails.product.id,
    )
    if (editedItem) {
      Object.assign(stoStore.formSTODetails, editedItem)
    }
  } else {
    stoStore.resetFormSTODetails()
  }
  isOpen.value = false
}

const columns = <QTableColumn[]>[
  {
    name: 'index',
    label: 'LOT',
    field: '',
    align: 'left' as const,
    sortable: false,
  },
  {
    name: 'quantity',
    label: 'เลขที่ผลิต',
    field: 'quantity',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'quantity',
    label: 'วันผลิต - หมดอายุ',
    field: 'quantity',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'quantity',
    label: 'ราคาทุน',
    field: 'quantity',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'quantity',
    label: 'คงเหลือ',
    field: 'quantity',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'orderQuantity',
    label: 'จำนวนสั่ง',
    field: 'quantity',
    align: 'center' as const,
    sortable: true,
  },
]
</script>

<style scoped>
:deep(.q-table thead tr) {
  font-size: 12pt;
  background-color: #83a7d8;
}

.row-table {
  align-items: center;
  height: 40px;
  background-color: #83a7d8;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  /* กำหนด 3 คอลัมน์ที่มีขนาดเท่ากัน */
  align-items: center;
  text-align: center;
}

.row-table-body {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  /* กำหนด 3 คอลัมน์ที่มีขนาดเท่ากัน */
  align-items: center;
  text-align: center;
}

.col-3 {
  padding: 8px;
}

.row-table .col-3 {
  flex: 1;
  text-align: center;
}

/* ทำให้ตัวเลขอยู่ชิดขวาสุดของเซลล์ */
.total-value {
  text-align: right;
  padding-right: 16px;
  /* ปรับระยะห่างให้ดูดี */
}

.total-row td {
  text-align: right;
  /* จัดข้อความให้ชิดขวาทุกช่อง */
  padding: 12px;
  /* ปรับระยะห่างให้ดูดีขึ้น */
}

.custom-table {
  border-radius: 10px;
  overflow: hidden;
}

.body-table {
  background-color: #ffffff;
}

.status-container {
  flex-direction: column;
  align-items: center;
  height: 40px;
  width: 165px;
  text-align: center;
  font-family: sans-serif;
}

.status-header {
  background-color: #83a7d8;
  padding: 10px;
  color: white;
  height: 40px;
  width: 170px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.status-body {
  background-color: #fff;
  padding: 10px;
  height: 40px;
  width: 170px;

  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.total-row {
  background-color: #83a7d8;
  color: black;
  font-weight: bold;
}

.body-table {
  background-color: #deecff;
}

.header-box {
  background-color: #83a7d8;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 40px;
  width: 160px;
  text-align: center;
}

.showDetail-Box {
  background-color: #ffffff;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  height: 40px;
  width: 160px;
  text-align: center;
}

.header-box2 {
  background-color: #83a7d8;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 40px;
  width: 160px;
  text-align: center;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.mini-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.btn-accept {
  background-color: #36b54d;
  margin-right: 10px;
}

.btn-cancel {
  background-color: #9c2c2c;
  margin-right: 10px;
}

.gap-container {
  margin-bottom: 20px;
}

.dialog-container {
  border-radius: 10px;
}

.input-container-v2 {
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  background-color: #83a7d8;
}

.input-container-v3 {
  padding-left: 10px;
  padding-right: 10px;
  margin-top: 5px;
  margin-bottom: 5px;
  border-radius: 5px;
  background-color: #d6e4f6;
  width: 150px;
}

.width-column {
  width: 300px;
}

.btn-add {
  background-color: #294888;
  color: white;
  border-radius: 5px;

  font-size: 12px;
}
</style>
