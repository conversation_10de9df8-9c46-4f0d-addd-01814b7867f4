import { Router } from "express";
import { StockTransferSlipLotDetailsController } from "../Controllers/StockTransferSlipLotDetails";

const router = Router();
const controller = new StockTransferSlipLotDetailsController();

router.get("/", controller.getAll.bind(controller));
router.get("/:id", controller.getById.bind(controller));
router.post("/sts/:stsId/details/:stsDetailsId", controller.addLotsToSlipItem.bind(controller));
router.put("/:lotDetailsId/sts-details/:stsDetailsId", controller.update.bind(controller));
router.put("/:stsDetailId/auto-fill", controller.autoFillQuantitySent.bind(controller));
router.delete("/:lotDetailsId/sts-details/:stsDetailsId", controller.delete.bind(controller));
export default router;
