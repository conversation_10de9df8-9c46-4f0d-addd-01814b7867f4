import "reflect-metadata";
import { DataSource } from "typeorm";
import { SupplierType } from "../Models/SupplierType";
import { Supplier } from "../Models/Supplier";
import { ProductGroup } from "../Models/ProductGroup";
import { Product } from "../Models/Product";
import { Stock } from "../Models/Stock";
import { Branch } from "../Models/Branch";
import { User } from "../Models/User";
import { PurchaseOrder } from "../Models/PurchaseOrder";
import { PurchaseOrderItem } from "../Models/PurchaseOrderItem";
import { SpecialReportGroup } from "../Models/SpecialReportGroup";
import { Attendance } from "../Models/Attendance";
import { LeaveRequest } from "../Models/LeaveRequest";
import { WorkingSchedule } from "../Models/WorkingSchedule";
import { ProductConversion } from "../Models/ProductConversion";
import { StockTransferOrder } from "../Models/StockTransferOrder";
import { StockTransferOrderDetails } from "../Models/StockTransferOrderDetails";
import { StockTransferSlip } from "../Models/StockTransferSlip";
import { StockTransferSlipDetails } from "../Models/StockTransferSlipDetails";
import { BranchReceive } from "../Models/BranchReceive";
import { BranchReceiveDetails } from "../Models/BranchReceiveDetails";
// import { ProductUnit } from "../Models/ProductUnit";
import { GoodsReceipt } from "../Models/GoodsReceipt";
import { GoodsReceiptDetails } from "../Models/GoodsReceiptDetails";
import { PaymentGoodsReceipt } from "../Models/PaymentGoodsReceipt";
import { StockTransferSlipLotDetails } from "../Models/StockTransferSlipLotDetails";

export const AppDataSource = new DataSource({
  type: "better-sqlite3",
  database: "./database2.sqlite",
  synchronize: false, // ตั้งค่าเป็น false ก่อน
  logging: false,
  entities: [
    SupplierType,
    Supplier,
    ProductGroup,
    Product,
    Stock,
    Branch,
    User,
    PurchaseOrder,
    PurchaseOrderItem,
    SpecialReportGroup,
    Attendance,
    LeaveRequest,
    WorkingSchedule,
    ProductConversion,
    StockTransferOrder,
    StockTransferOrderDetails,
    StockTransferSlip,
    StockTransferSlipDetails,
    BranchReceive,
    BranchReceiveDetails,
    // ProductUnit,
    GoodsReceipt,
    GoodsReceiptDetails,
    PaymentGoodsReceipt,
    StockTransferSlipLotDetails,
  ],
});

AppDataSource.initialize()
  .then(async () => {
    console.log("✅ Connected to SQLite database");

    // // ลบตาราง supplier หากอยู่
    // await AppDataSource.query(`DROP TABLE IF EXISTS supplier;`);
    // await AppDataSource.query(`DROP TABLE IF EXISTS supplierType;`); // ลบ SupplierType หากจำเป็น
    // await AppDataSource.query(`DROP TABLE IF EXISTS payment_goods_receipt;`);
    // await AppDataSource.query(`DROP TABLE IF EXISTS goods_receipt_details;`);
    // await AppDataSource.query(`DROP TABLE IF EXISTS goods_receipt;`);

    // ลบ SupplierType หากจำเป็น
    // await AppDataSource.query(`DROP TABLE IF EXISTS stock_transfer_slip_lot_details;`);
    // await AppDataSource.query(`DROP TABLE IF EXISTS branch_receive_details;`);
    // await AppDataSource.query(`DROP TABLE IF EXISTS branch_receive;`);
    // await AppDataSource.query(`DROP TABLE IF EXISTS stock_transfer_slip_details;`);
    // await AppDataSource.query(`DROP TABLE IF EXISTS stock_transfer_slip;`);
    // await AppDataSource.query(`DROP TABLE IF EXISTS stock_transfer_order_details;`);
    // await AppDataSource.query(`DROP TABLE IF EXISTS stock_transfer_order;`);

    try {
      // Check if the new columns exist, if not add them
      const tableInfo = await AppDataSource.query(`PRAGMA table_info(user);`);
      const columnNames = tableInfo.map((col: any) => col.name);

      if (!columnNames.includes("sick_leave_remaining")) {
        await AppDataSource.query(
          `ALTER TABLE user ADD COLUMN sick_leave_remaining INTEGER DEFAULT 0;`
        );
        console.log("✅ Added sick_leave_remaining column");
      }

      if (!columnNames.includes("personal_leave_remaining")) {
        await AppDataSource.query(
          `ALTER TABLE user ADD COLUMN personal_leave_remaining INTEGER DEFAULT 0;`
        );
        console.log("✅ Added personal_leave_remaining column");
      }

      // Check and add expiration tracking columns to product table
      const productTableInfo = await AppDataSource.query(
        `PRAGMA table_info(product);`
      );
      const productColumnNames = productTableInfo.map((col: any) => col.name);

      if (!productColumnNames.includes("manufactureDate")) {
        await AppDataSource.query(
          `ALTER TABLE product ADD COLUMN manufactureDate DATE;`
        );
        console.log("✅ Added manufactureDate column to product table");
      }

      if (!productColumnNames.includes("expirationDate")) {
        await AppDataSource.query(
          `ALTER TABLE product ADD COLUMN expirationDate DATE;`
        );
        console.log("✅ Added expirationDate column to product table");
      }

      // Check if working_schedule table exists, if not create it
      const tableExists = await AppDataSource.query(
        `SELECT name FROM sqlite_master WHERE type='table' AND name='working_schedule';`
      );

      if (tableExists.length === 0) {
        // Table doesn't exist, create it
        await AppDataSource.query(`
          CREATE TABLE working_schedule (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            userId INTEGER NOT NULL,
            standard_check_in_time TIME DEFAULT '09:00:00',
            standard_check_out_time TIME DEFAULT '17:00:00',
            late_threshold_minutes INTEGER DEFAULT 5,
            early_threshold_minutes INTEGER DEFAULT 30,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (userId) REFERENCES user(id) ON DELETE CASCADE
          );
        `);
        console.log("✅ Created working_schedule table");

        // Create default schedules for existing users
        const existingUsers = await AppDataSource.query(`SELECT id FROM user;`);
        for (const user of existingUsers) {
          await AppDataSource.query(
            `
            INSERT INTO working_schedule (userId, standard_check_in_time, standard_check_out_time, late_threshold_minutes, early_threshold_minutes, is_active)
            VALUES (?, '09:00:00', '17:00:00', 5, 30, 1);
          `,
            [user.id]
          );
        }
        console.log("✅ Created default working schedules for existing users");
      } else {
        console.log("✅ working_schedule table already exists");
      }

      // Check and add location columns to attendance table
      const attendanceTableInfo = await AppDataSource.query(
        `PRAGMA table_info(attendance);`
      );
      const attendanceColumnNames = attendanceTableInfo.map(
        (col: any) => col.name
      );

      if (!attendanceColumnNames.includes("check_in_latitude")) {
        await AppDataSource.query(
          `ALTER TABLE attendance ADD COLUMN check_in_latitude REAL;`
        );
        console.log("✅ Added check_in_latitude column to attendance table");
      }

      if (!attendanceColumnNames.includes("check_in_longitude")) {
        await AppDataSource.query(
          `ALTER TABLE attendance ADD COLUMN check_in_longitude REAL;`
        );
        console.log("✅ Added check_in_longitude column to attendance table");
      }

      if (!attendanceColumnNames.includes("check_in_accuracy")) {
        await AppDataSource.query(
          `ALTER TABLE attendance ADD COLUMN check_in_accuracy REAL;`
        );
        console.log("✅ Added check_in_accuracy column to attendance table");
      }

      if (!attendanceColumnNames.includes("check_out_latitude")) {
        await AppDataSource.query(
          `ALTER TABLE attendance ADD COLUMN check_out_latitude REAL;`
        );
        console.log("✅ Added check_out_latitude column to attendance table");
      }

      if (!attendanceColumnNames.includes("check_out_longitude")) {
        await AppDataSource.query(
          `ALTER TABLE attendance ADD COLUMN check_out_longitude REAL;`
        );
        console.log("✅ Added check_out_longitude column to attendance table");
      }

      if (!attendanceColumnNames.includes("check_out_accuracy")) {
        await AppDataSource.query(
          `ALTER TABLE attendance ADD COLUMN check_out_accuracy REAL;`
        );
        console.log("✅ Added check_out_accuracy column to attendance table");
      }

      console.log("✅ Database schema updated successfully");
    } catch (error) {
      console.error("❌ Error updating database schema:", error);
    }
  })
  .catch((error) => {
    console.error("❌ Error connecting to SQLite:", error);
  });
