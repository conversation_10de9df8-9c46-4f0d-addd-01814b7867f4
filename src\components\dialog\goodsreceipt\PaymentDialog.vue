<template>
  <q-dialog v-model="isOpen">
    <q-card style="max-width: 1000px; width: 950px">
      <q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">รายละเอียดการชำระเงิน</div>
          <q-btn icon="close" @click="closeDialog" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <div class="shadow-2">
        <div class="row justify-between">
          <div class="col-11 row q-my-sm type-payment">
            <div class="col-2 q-pr-md flex flex-center"
              style="margin-left: 30px; white-space: nowrap; font-weight: bold; font-size: 16px">
              ประเภทการชำระ
            </div>
            <div class="row q-col-gutter-sm" style="margin-left: 5px">
              <q-radio keep-color v-model="store.formPayment.payment_type" val="เงินโอน" label="เงินโอน"
                color="blue-grey-9" size="sm" />
              <q-radio keep-color v-model="store.formPayment.payment_type" val="เงินสด" label="เงินสด"
                color="blue-grey-9" size="sm" />
              <q-radio keep-color v-model="store.formPayment.payment_type" val="บัตรเครดิต" label="บัตรเครดิต"
                color="blue-grey-9" size="sm" />
              <q-radio keep-color v-model="store.formPayment.payment_type" val="เช็คธนาคาร" label="เช็คธนาคาร"
                color="blue-grey-9" size="sm" />
              <q-radio keep-color v-model="store.formPayment.payment_type" val="เครดิตเจ้าหนี้" label="เครดิตเจ้าหนี้"
                color="blue-grey-9" size="sm" />
              <q-radio keep-color v-model="store.formPayment.payment_type" val="อื่นๆ" label="อื่นๆ" color="blue-grey-9"
                size="sm" />
            </div>
          </div>
        </div>
      </div>
      <q-card-section style="overflow: auto; max-height: 80vh">
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center" style="font-size: medium">
            รายละเอียดการชำระเงิน
          </div>
          <div class="shadow-2 container">
            <div class="row q-col-gutter-md">
              <!-- วันที่ -->
              <div class="col-3 width-column" style="margin-left: 10px">
                <div class="q-mr-sm" style="font-size: 15px; text-align: center">วันที่</div>
                <q-input dense borderless class="input-container" v-model="formattedDate" />
              </div>

              <!-- ผู้รับ -->
              <div class="col-3 width-column" style="margin-left: 10px">
                <div class="q-mr-sm" style="font-size: 15px; text-align: center">ผู้รับ</div>
                <q-input dense borderless class="input-container" v-model="store.formPayment.receivedBy.name" />
              </div>

              <!-- รายละเอียด -->
              <div>
                <div class="col-3" style="margin-left: 10px">
                  <div class="q-mr-sm" style="font-size: 15px; text-align: center; margin-left: 80px">
                    รายละเอียด
                  </div>
                  <div class="col-3 row items-center">
                    <div class="q-mr-sm" style="margin-left: 20px; font-size: 15px">เงินสด :</div>
                    <q-input dense borderless class="input-container" v-model="store.formPayment.cash_amount"
                      type="number" />
                  </div>
                  <div class="col-3 row">
                    <div class="q-mr-sm" style="margin-top: 10px; font-size: 15px">หมายเหตุ :</div>
                    <q-input type="textarea" dense borderless class="input-container" v-model="store.formPayment.note"
                      style="margin-top: 10px" />
                  </div>
                </div>
              </div>
              <!-- จำนวนเงิน -->
              <div class="col-3 width-column">
                <div class="q-mr-sm" style="font-size: 15px; text-align: center">จำนวนเงิน</div>
                <q-input dense borderless class="input-container" v-model="store.formPayment.total_amount"
                  type="number" />
              </div>
            </div>
          </div>
        </div>
        <q-card-actions align="center">
          <q-btn class="btn-accept" dense flat label="บันทึก" color="white" @click="savePayment" />

          <q-btn class="btn-cancel" dense flat label="ยกเลิก" color="white" @click="closeDialog" />
        </q-card-actions>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>
<script setup lang="ts">
import { date } from 'quasar'
import { useAuthStore } from 'src/stores/authStore'
import { useGoodsReceiptStore } from 'src/stores/goodsreceipt'
import { useGoodsReceiptPaymentStore } from 'src/stores/goodsReceiptPayment'
import { useUserStore } from 'src/stores/userStore'
import { computed, watch } from 'vue'
interface Props {
  modelValue: boolean
  mode?: string
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'add'
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'item-updated': []
}>()

const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})
const store = useGoodsReceiptPaymentStore()
const grStore = useGoodsReceiptStore()
const authStore = useAuthStore()
const userStore = useUserStore()
const closeDialog = () => {
  isOpen.value = false
  store.resetForm()
}
watch(
  () => props.modelValue,
  () => {
    store.formPayment.payment_date = new Date()
    if (!grStore.form.distributor) {
      store.formPayment.receivedBy = {
        id: 0,
        supplier_number: '',
        name: '',
        address: '',
        tel: '',
        tax_number: '',
        contact_name: '',
        fax: '',
        email: '',
        type: { id: 0, name: '' },
        isactive: false, //
        credit_days: 0,
      }
    } else {
      store.formPayment.receivedBy = grStore.form.distributor
    }
    const currentUser = authStore.currentUser || userStore.currentUser
    if (currentUser) {
      store.formPayment.user = {
        id: currentUser.id,
        password: currentUser.password || '',
        name: currentUser.name,
        image: currentUser.image || '',
        tel: currentUser.tel || '',
        role: currentUser.role || '',
        hour_work: currentUser.hour_work || 0,
        sick_level: currentUser.sick_level || 0,
        personal_leave: currentUser.personal_leave || 0,
        branch: currentUser.branch || {
          id: 0,
          name: '',
          address: '',
        },
      }
    } else {
      // Fallback if no current user
      store.formPayment.user = {
        id: 0,
        password: '',
        name: '',
        image: '',
        tel: '',
        role: '',
        hour_work: 0,
        sick_level: 0,
        personal_leave: 0,
        branch: {
          id: 0,
          name: '',
          address: '',
          contact_name: '',
          contact_phone: '',
        },
      }
    }
    console.log(store.formPayment.user)
    store.formPayment.total_amount = grStore.form.tax_total
    store.formPayment.payment_type = 'เงินสด'
    store.formPayment.cash_amount = grStore.form.tax_total

  }
)
const unpaid_amount = computed(() => {
  if (store.formPayment.total_amount && store.formPayment.cash_amount) {
    return store.formPayment.total_amount - store.formPayment.cash_amount
  }
  return 0
})
const change_amount = computed(() => {
  if (store.formPayment.total_amount && store.formPayment.cash_amount) {
    if (store.formPayment.cash_amount > store.formPayment.total_amount) {
      return store.formPayment.cash_amount - store.formPayment.total_amount
    }
  }
  return 0
})
const savePayment = async () => {
  store.formPayment.unpaid_amount = unpaid_amount.value
  store.formPayment.paid_amount = store.formPayment.cash_amount
  store.formPayment.change_amount = change_amount.value
  await store.createByGR(grStore.form.id, store.formPayment)
  await store.fetchByGRId(grStore.form.id)
  isOpen.value = false
  store.resetForm()
}

const formattedDate = computed({
  get() {
    if (!store.formPayment || !store.formPayment.payment_date) {
      return '';
    }
    return date.formatDate(store.formPayment.payment_date, 'DD/MM/YYYY') // แปลงจาก Date เป็น string ในรูปแบบ YYYY-MM-DD
  },
  set(value: string) {
    if (!store.formPayment) return;
    // if (store.form.status == 'เตรียมรายการ') {
    //   store.form.tax_invoice_date = new Date('');
    // } else if (store.form.status == 'ดำเนินการ') {
    //   const newDate = new Date(value) // แปลงจาก string กลับเป็น Date
    //   store.form.tax_invoice_date = newDate // อัพเดทค่าใน store
    // }
    const parts = value.split('/')
    if (parts.length === 3) {
      const [day, month, year] = parts
      const newDate = new Date(`${year}-${month}-${day}T00:00:00`)
      store.formPayment.payment_date = newDate // ✅ เก็บเป็น Date ตาม type
    } else {
      store.formPayment.payment_date = new Date()
    }
  },
})

</script>
<style scoped>
.type-payment {
  margin-left: 10px;
  margin-top: 20px;
}

.gap-container {
  margin-bottom: 20px;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.width-column {
  width: 190px;
}

.input-container {
  background-color: white;
  border-radius: 5px;
  width: 190px;
  padding-left: 10px;
  padding-right: 10px;
}

.btn-accept {
  background-color: #36b54d;
  margin-right: 10px;
  width: 80px;
}

.btn-cancel {
  background-color: #9c2c2c;
  margin-right: 10px;
  width: 80px;
}
</style>
