import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { PurchaseOrderItem } from "../Models/PurchaseOrderItem";
import { Product } from "../Models/Product";
import { In } from "typeorm";
import { Stock } from "../Models/Stock";
import { GoodsReceiptDetails } from "../Models/GoodsReceiptDetails";
import { GoodsReceipt } from "../Models/GoodsReceipt";

export class GoodsReceiptDetailsController {
  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const grDetailsRepository = AppDataSource.getRepository(GoodsReceiptDetails);
      const grDetails = await grDetailsRepository.find({ relations: ["gr", "product"] });
      res.status(200).json(grDetails);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const grDetailsRepository = AppDataSource.getRepository(GoodsReceiptDetails);
      const grDetails = await grDetailsRepository.findOne({
        where: { id: Number(id) },
        relations: ["gr", "product"]
      });
      if (grDetails) {
        res.status(200).json(grDetails);
      } else {
        res.status(404).json({ message: "GR details not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getByProductId(req: Request, res: Response): Promise<void> { // for look lot before
    const { productId, branchId } = req.params;
    if (!productId || !branchId) {
      res.status(400).json({ message: "Missing productId or branchId" });
      return;
    }
    try {
      const grDetailsRepository = AppDataSource.getRepository(GoodsReceiptDetails);
      const latestGRDetail = await grDetailsRepository.findOne({
        where: {
          product: { id: Number(productId) },
          gr: {
            branch: { id: Number(branchId) },
            status: 'เสร็จสมบูรณ์'
          },
        },
        order: {
          gr: {
            date_document: "DESC" // receive_date
          }
        },
        relations: ["gr", "product"],
      });
      if (latestGRDetail) {
        res.status(200).json(latestGRDetail);
      } else {
        res.status(200).json({});
      }

    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }
  public async getByGr(req: Request, res: Response): Promise<void> {
    const { grId } = req.params;
    try {
      const grDetailsRepository = AppDataSource.getRepository(GoodsReceiptDetails);
      const grDetails = await grDetailsRepository.find({
        where: { gr: { id: Number(grId) } },
        relations: ["product", "product.distributor"]
      });
      if (grDetails) {
        res.status(200).json(grDetails);
      } else {
        res.status(404).json({ message: "GR details not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }
  public async create(req: Request, res: Response): Promise<void> { //manual
    const grDetailsRepository = AppDataSource.getRepository(GoodsReceiptDetails);
    const grRepository = AppDataSource.getRepository(GoodsReceipt);
    const productRepository = AppDataSource.getRepository(Product);
    const stockRepository = AppDataSource.getRepository(Stock);
    try {
      const grId = parseInt(req.params.grId, 10);
      if (isNaN(grId)) {
        res.status(400).json({ message: "Invalid grId" });
        return;
      }

      const grDetails = req.body;
      if (!Array.isArray(grDetails)) {
        res.status(400).json({ message: "Invalid request body" });
        return;
      }

      const gr = await grRepository.findOne({
        where: { id: grId },
        relations: ["distributor"]
      });

      if (!gr) {
        res.status(404).json({ message: "GR Details not found" });
        return;
      }

      if (gr.status !== 'เตรียมรายการ') {
        res.status(400).json({ message: "GR status must be 'เตรียมรายการ' to update" });
        return;
      }

      // ✅ ดึงข้อมูล GR Items ที่มีอยู่เดิม
      const existingItems = await grDetailsRepository.find({
        where: { gr: { id: grId } }
      });

      // ✅ ดึงข้อมูลสินค้า
      const productIds = grDetails
        .filter(item => typeof item.product === 'object' || typeof item.product_id === 'number')
        .map(item => (typeof item.product === 'object' ? item.product.id : item.product_id));

      const products = await productRepository.find({ where: productIds.map(id => ({ id })) });

      // ดึง stock ตามสินค้าที่เกี่ยวข้อง + branch ต้นทาง version สร้าง sts เอง
      const allStock = await stockRepository.find({
        where: {
          product: In(productIds),
          branch: { id: gr.distributor.id }
        },
        relations: ["product", "branch"]
      });

      //ภาษี รวมภาษี
      let tax = 0.00
      let tax_total = 0.00
      // ✅ เตรียมรายการใหม่ (เพิ่ม + อัปเดต)
      const updatedItems = grDetails.map(item => {
        let product: Product | undefined;

        if (typeof item.product === 'object') {
          product = products.find(p => p.id === item.product.id);
        } else if (typeof item.product_id === 'number') {
          product = products.find(p => p.id === item.product_id);
        }

        if (!product) return null;

        const stockMatch = allStock.find(
          stock => stock.product.id === product!.id
          // && stock.branch.id === gr.distributor.id
        );

        const lot_number = stockMatch?.lot_number ?? '';

        // tax_total += (item.quantity * item.unit_price)
        // tax = ((item.quantity * item.unit_price) * 7) / 100

        if (item.id === 0) {
          return grDetailsRepository.create({
            gr: gr,
            product,
            lot_number_before: lot_number,
            receive_quantity: item.receive_quantity,
            receive_unit: item.receive_unit,
            receive_price_before_tax: item.receive_price_before_tax,
            receive_price_after_discount: item.receive_price_after_discount,
            free_quantity: item.free_quantity,
            free_unit: item.free_unit,
            total_receive_quantity: item.total_receive_quantity,  //+free_quantity
            cost_unit: item.cost_unit,
            total_price_product: item.total_price_product,
            gr_total: item.gr_total

          });
        } else {
          const existingItem = existingItems.find(i => i.id === item.id);
          if (existingItem) {
            existingItem.receive_quantity = item.receive_quantity;
            existingItem.receive_unit = item.receive_unit;
            existingItem.receive_price_before_tax = item.receive_price_before_tax
            existingItem.receive_price_after_discount = item.receive_price_after_discount
            existingItem.free_quantity = item.free_quantity;
            existingItem.free_unit = item.free_unit;
            existingItem.total_receive_quantity = item.total_receive_quantity
            existingItem.cost_unit = item.cost_unit
            existingItem.total_price_product = item.total_price_product
            existingItem.gr_total = item.gr_total
            return existingItem;
          }
        }
        return null;
      }).filter((item): item is GoodsReceiptDetails => item !== null);

      tax = (tax_total * 7) / 100
      tax_total += tax
      // await grRepository.update(gr.id, {
      //   tax: tax,
      //   tax_total: tax_total
      // });

      // ✅ ลบ Order Items ที่ไม่มีอยู่ในรายการใหม่
      const receivedItemIds = grDetails.map(item => item.id);
      const itemsToDelete = existingItems.filter(item => !receivedItemIds.includes(item.id));

      if (itemsToDelete.length > 0) {
        await grDetailsRepository.remove(itemsToDelete);
      }

      // ✅ ป้องกัน `save([])`
      if (updatedItems.length > 0) {
        const savedItems = await grDetailsRepository.save(updatedItems);
        await grRepository.save(gr);

        res.status(201).json(savedItems);
      } else {
        res.status(200).json({
          grDetails: [],
        });
      }

    } catch (error) {
      console.error("Error updating GR Details:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async updateGRDetailsFromPO(req: Request, res: Response): Promise<void> { //uopdate grdetails ที่ create from po
    const grDetailsRepository = AppDataSource.getRepository(GoodsReceiptDetails);
    const grRepository = AppDataSource.getRepository(GoodsReceipt);
    const productRepository = AppDataSource.getRepository(Product);

    const grId = req.params.grId;
    // if (isNaN(grId)) {
    //   res.status(400).json({ message: "Invalid grId" });
    //   return;
    // }

    const incomingItems = req.body;
    if (!Array.isArray(incomingItems)) {
      res.status(400).json({ message: "Invalid request body" });
      return;
    }

    const gr = await grRepository.findOne({ where: { id: Number(grId) } });
    if (!gr) {
      res.status(404).json({ message: "GR not found" });
      return;
    }

    // ดึงรายการเดิมทั้งหมดในฐานข้อมูล
    const existingItems = await grDetailsRepository.find({
      where: { gr: { id: Number(grId) } },
      relations: ['product']
    });

    // ดึง products ที่เกี่ยวข้องมาเพื่อ map
    const productIds = incomingItems.map(i => i.product?.id || i.product_id);
    const products = await productRepository.findBy(productIds);

    const itemsToUpdate = incomingItems.map(item => {
      const existingItem = existingItems.find(i => i.id === item.id);
      if (!existingItem) return null;

      const product = products.find(p =>
        typeof item.product === 'object'
          ? p.id === item.product.id
          : p.id === item.product_id
      );
      if (!product) return null;
      // if (item.sts_quantity == 0){
      //   existingItem.sts_details_status
      // }
      existingItem.product = product;
      existingItem.receive_quantity = item.receive_quantity;
      existingItem.receive_unit = item.receive_unit;
      existingItem.receive_price_before_tax = item.receive_price_before_tax
      existingItem.receive_price_after_discount = item.receive_price_after_discount
      existingItem.free_quantity = item.free_quantity;
      existingItem.free_unit = item.free_unit;
      existingItem.total_receive_quantity = item.total_receive_quantity
      existingItem.cost_unit = item.cost_unit
      existingItem.total_price_product = item.total_price_product

      return existingItem;
    }).filter((item): item is GoodsReceiptDetails => item !== null);

    await grDetailsRepository.save(itemsToUpdate);

    res.status(200).json(itemsToUpdate);
  }


  public async update(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const grDetailsRepository = AppDataSource.getRepository(GoodsReceiptDetails);
    try {
      const grDetails = await grDetailsRepository.findOne({ where: { id: Number(id) } });
      if (grDetails) {
        grDetailsRepository.merge(grDetails, req.body);
        const updatedgrDetails = await grDetailsRepository.save(grDetails);
        res.status(200).json(updatedgrDetails);
      } else {
        res.status(404).json({ message: "GR Details not found" });
      }
    } catch (error) {
      res.status(400).json({ message: "Error updating GR Details" });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const grDetailsRepository = AppDataSource.getRepository(GoodsReceiptDetails);
    try {
      const result = await grDetailsRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "GR Details not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async updateMultiple(req: Request, res: Response): Promise<void> {
    const purchaseOrderItemRepository = AppDataSource.getRepository(PurchaseOrderItem);

    try {
      const purchase_order_id = Number(req.params.id);
      console.log("Received purchase_order_id:", purchase_order_id);
      console.log("Received body:", req.body);

      // ถ้า req.body เป็น array ให้ใช้ได้เลย, ถ้าเป็น object ให้ดึง items
      const items = Array.isArray(req.body) ? req.body : req.body.items;

      if (!items || !Array.isArray(items) || items.length === 0) {
        res.status(400).json({ message: "Invalid request data" });
        return;
      }

      // หา PurchaseOrderItem ทั้งหมดที่ต้องอัปเดต
      const itemIds = items.map((item) => item.id);
      const existingItems = await purchaseOrderItemRepository.findBy({ id: In(itemIds) });
      if (existingItems.length !== items.length) {
        res.status(404).json({ message: "Some Purchase Order Items not found" });
        return;
      }

      // อัปเดตค่าใหม่
      const updatedItems = existingItems.map((existingItem) => {
        const newItem = items.find((item) => item.id === existingItem.id);
        if (newItem) {
          return {
            ...existingItem,
            quantity: newItem.quantity,
            unit_price: newItem.unit_price,
            total_price: newItem.quantity * newItem.unit_price,
          };
        }
        return existingItem;
      });

      // บันทึกการอัปเดตทั้งหมด
      await purchaseOrderItemRepository.save(updatedItems);

      res.status(200).json({ message: "Purchase Order Items updated successfully", updatedItems });
    } catch (error) {
      res.status(500).json({ message: "Error updating Purchase Order Items", error });
    }
  }


}
