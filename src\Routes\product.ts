import { Router } from "express";
import { ProductController } from "../Controllers/product";
import { ProductConversionController } from "../Controllers/productConversion";

const router = Router();
const controller = new ProductController();
const conversionController = new ProductConversionController();

router.get("/", controller.getAll.bind(controller));
router.get(
  "/expiration-alerts",
  controller.getExpirationAlerts.bind(controller)
);
router.get("/name/:name", controller.getByName.bind(controller));
router.get("/:id", controller.getById.bind(controller));
router.post("/", controller.create.bind(controller));
router.put("/:id", controller.update.bind(controller));
router.delete("/:id", controller.delete.bind(controller));
router.post("/filter", controller.getAllByFilter.bind(controller));
router.post("/bulk", controller.createBulk.bind(controller));

// Add the break endpoint
router.post(
  "/:id/break",
  conversionController.breakProduct.bind(conversionController)
);

// Add the combine endpoint
router.post(
  "/:id/combine",
  conversionController.combineProduct.bind(conversionController)
);

export default router;
