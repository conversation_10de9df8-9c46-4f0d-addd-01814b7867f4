import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { PurchaseOrder } from "../Models/PurchaseOrder";
import { User } from "../Models/User";
import { Branch } from "../Models/Branch";
import { StockTransferOrder } from "../Models/StockTransferOrder";
import { Stock } from "../Models/Stock";
import { StockTransferSlip } from "../Models/StockTransferSlip";
import { StockTransferSlipDetails } from "../Models/StockTransferSlipDetails";
import { MoreThan } from "typeorm";
import { TransferStatus } from "../enums/transfer-status";
import { LotStatus } from "../enums/lot-status";
import { StockTransferSlipLotDetails } from "../Models/StockTransferSlipLotDetails";

export class StockTransferSlipController {

  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const stsRepository = AppDataSource.getRepository(StockTransferSlip);
      const sts = await stsRepository.find({
        relations: ["sto", "user", "source_branch", "destination_branch", "sts_details"]
      });
      res.status(200).json(sts);
    } catch (error) {
      console.error("Error in getAll:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const stsRepository = AppDataSource.getRepository(StockTransferSlip);
      const sts = await stsRepository.findOne({
        where: { id: Number(id) },
        relations: ["sto", "user", "source_branch", "destination_branch", "sts_details", "sts_details.product"]
      });
      if (sts) {
        res.status(200).json(sts);
      } else {
        res.status(404).json({ message: "STS not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getProductByBranch(req: Request, res: Response): Promise<void> { //source branch
    const { id } = req.params;
    try {
      const stockRepository = AppDataSource.getRepository(Stock);
      const stsRepository = AppDataSource.getRepository(StockTransferSlip)
      const sts = await stsRepository.findOne({
        where: { id: Number(id) },
        relations: ["sto", "user", "source_branch", "destination_branch", "sts_details", "sts_details.product"]
      });
      if (!sts) {
        res.status(404).json({ message: "STS not found" });
        return;
      }
      const product = await stockRepository.find({
        where: { branch: { id: sts?.source_branch.id } },
        relations: ["product"]
      })
      if (product) {
        res.status(200).json({
          branch: sts.source_branch,
          products: product,
        });
      } else {
        res.status(404).json({ message: "Product not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getByStatus(req: Request, res: Response): Promise<void> {
    const { status } = req.params;
    try {
      const stsRepository = AppDataSource.getRepository(StockTransferSlip);
      const list = await stsRepository.find({
        where: { status }, // status: 'เตรียมรายการ' หรือ 'กำลังดำเนินการ'
        relations: ["sto", "user", "source_branch", "destination_branch", "sts_details", "sts_details.product"],
        order: { transfer_date: "DESC" }
      });
      res.status(200).json(list);
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getAllByFilter(req: Request, res: Response): Promise<void> {
    const { search, filter, startDate, endDate } = req.body;

    try {
      const purchaseOrderRepository = AppDataSource.getRepository(PurchaseOrder);

      const query = purchaseOrderRepository
        .createQueryBuilder("purchase_order")
        .leftJoinAndSelect("purchase_order.supplier", "supplier")
        .leftJoinAndSelect("purchase_order.user", "user")
        .where("1=1");

      // กรองตามคำค้นหาที่ส่งมา
      if (search) {
        query.andWhere(
          `(
            purchase_order.code LIKE :search
            OR user.name LIKE :search
            OR purchase_order.status LIKE :search
          )`,
          { search: `%${search}%` }
        );
      }

      if (filter && search) {
        query.andWhere(`purchase_order.${filter} LIKE :search`, {
          search: `%${search}%`,
        });
      }

      if (startDate) {
        query.andWhere("DATE(purchase_order.date) >= :startDate", {
          startDate: startDate,
        });
      }

      if (endDate) {
        query.andWhere("DATE(purchase_order.date) <= :endDate", {
          endDate: endDate,
        });
      }

      const purchaseOrders = await query.getMany();
      res.status(200).json(purchaseOrders);
    } catch (error) {
      console.error("Error filtering purchase orders:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async create(req: Request, res: Response): Promise<void> {
    const userRepository = AppDataSource.getRepository(User);
    const branchRepository = AppDataSource.getRepository(Branch);
    const stsRepository = AppDataSource.getRepository(StockTransferSlip);

    const {
      source_branch,
      destination_branch,
      transfer_date,
      user,
      note = '',
      sts_price = 0,
      sts_details = [] // ถ้าไม่ได้ส่งมาก็ให้เป็น array ว่าง
    } = req.body;

    try {
      const userEntity = await userRepository.findOneByOrFail({ id: user.id });
      const sourceBranch = await branchRepository.findOneByOrFail({ id: source_branch.id });
      const destinationBranch = await branchRepository.findOneByOrFail({ id: destination_branch.id });

      // แปลงวันที่เป็น YYYYMMDD
      const formattedDate = new Date(transfer_date).toISOString().slice(0, 10).replace(/-/g, "");

      // ดึง ID ล่าสุด เพื่อใช้ในการ gen code
      const lastSts = await stsRepository.find({
        order: { id: "DESC" },
        take: 1
      });

      const lastId = lastSts.length > 0 ? lastSts[0].id + 1 : 1;

      // สร้างรหัส
      const generatedCode = `STS-${formattedDate}-${lastId}`;

      // สร้าง STO ใหม่
      const newSts = stsRepository.create({
        code: generatedCode,
        sto_code: '',
        source_branch: sourceBranch,
        destination_branch: destinationBranch,
        transfer_date,
        user: userEntity,
        note,
        status: 'เตรียมรายการ', // กำหนดสถานะเริ่มต้น
        sts_price,
        sts_details, // จะเป็น [] ถ้าไม่ได้ส่งมา
        transfer_status: TransferStatus.PREPARING
      });

      const savedSTO = await stsRepository.save(newSts);

      res.status(201).json(savedSTO);
    } catch (error) {
      console.error("Error creating STS:", error);
      res.status(500).json({ message: "Failed to create STS", error });
    }
  }

  public async createSTSBySTO(req: Request, res: Response): Promise<void> {
    const stsRepository = AppDataSource.getRepository(StockTransferSlip);
    const stoRepository = AppDataSource.getRepository(StockTransferOrder);
    const stockRepository = AppDataSource.getRepository(Stock);
    const stsDetailRepository = AppDataSource.getRepository(StockTransferSlipDetails);
    const lotDetailRepository = AppDataSource.getRepository(StockTransferSlipLotDetails);

    const { stoId } = req.params;
    try {
      const sto = await stoRepository.findOneOrFail({
        where: { id: Number(stoId) },
        relations: [
          "user",
          "source_branch",
          "destination_branch",
          "sto_details",
          "sto_details.product"
        ]
      });

      const formattedDate = new Date().toISOString().slice(0, 10).replace(/-/g, "");
      const lastSts = await stsRepository.find({ order: { id: "DESC" }, take: 1 });
      const lastId = lastSts.length > 0 ? lastSts[0].id + 1 : 1;
      const generatedCode = `STS-${formattedDate}-${lastId}-STO-${sto.code}`;

      let count = 0;
      await Promise.all(
        sto.sto_details.map(async (detail) => {
          const lotStock = await stockRepository.findOne({
            where: {
              branch: { id: sto.destination_branch.id },
              product: { id: detail.product.id }
            }
          });
          if (!lotStock || lotStock.remaining < detail.quantity) count += 1;
        })
      );

      const newStatus = count > 0 ? TransferStatus.PREPARING : TransferStatus.WAITING_TO_SEND;
      await stoRepository.update(sto.id, { transfer_status: newStatus });

      const newSts = stsRepository.create({
        sto,
        code: generatedCode,
        sto_code: sto.code,
        source_branch: sto.destination_branch,
        destination_branch: sto.source_branch,
        transfer_date: new Date(),
        user: sto.user,
        note: sto.note,
        status: 'เตรียมรายการ',
        sts_price: 0,
        transfer_status: newStatus
      });

      const savedSTS = await stsRepository.save(newSts);

      const stsDetails: StockTransferSlipDetails[] = [];

      for (const detail of sto.sto_details) {
        const stocks = await stockRepository.find({
          where: {
            branch: { id: sto.destination_branch.id },
            product: { id: detail.product.id },
            remaining: MoreThan(0)
          },
          order: { id: "ASC" },
          relations: ["product", "branch"]
        });

        let qtyRemain = detail.quantity;
        const lotDetails: StockTransferSlipLotDetails[] = [];
        let totalSent = 0;

        for (const stock of stocks) {
          if (qtyRemain <= 0) break;

          const takeQty = Math.min(qtyRemain, stock.remaining);
          qtyRemain -= takeQty;
          totalSent += takeQty;

          const lotDetail = lotDetailRepository.create({
            stock,
            quantity_ordered: detail.quantity,
            quantity_sent: takeQty,
            lot_status:
              takeQty === detail.quantity
                ? LotStatus.MATCHED
                : takeQty === 0
                  ? LotStatus.NONE
                  : takeQty < detail.quantity
                    ? LotStatus.LESS
                    : LotStatus.MORE
          });
          lotDetails.push(lotDetail);
        }

        if (lotDetails.length === 0) {
          lotDetails.push(
            lotDetailRepository.create({
              quantity_ordered: detail.quantity,
              quantity_sent: 0,
              lot_status: LotStatus.NONE
            })
          );
        }

        const stsDetail = stsDetailRepository.create({
          product: detail.product,
          total_quantity_ordered: detail.quantity,
          total_quantity_sent: totalSent,
          sts_details_status:
            totalSent === detail.quantity
              ? 'จำนวนส่งพอดี'
              : totalSent === 0
                ? 'ไม่มีจำนวนส่ง'
                : 'จำนวนส่งน้อยกว่า',
          lot_number: lotDetails.map(l => l.stock?.lot_number).filter(Boolean).join(', '),
          sto_details: detail,
          sts: savedSTS,
          lot_details: await lotDetailRepository.save(lotDetails)
        });

        stsDetails.push(await stsDetailRepository.save(stsDetail));
      }

      savedSTS.sts_details = stsDetails;
      await stsRepository.save(savedSTS);
      await stoRepository.update(sto.id, { sts: savedSTS });

      res.status(201).json({ message: "สร้างใบ STS สำเร็จ", sts: savedSTS });
    } catch (error) {
      console.error("Error creating STS by STO:", error);
      res.status(500).json({ message: "เกิดข้อผิดพลาดในการสร้าง STS", error });
    }
  }

  public async updateSTS(req: Request, res: Response): Promise<void> { //update อันที่ create sto-> sts
    const { id } = req.params; //
    const stsRepository = AppDataSource.getRepository(StockTransferSlip);
    const stoRepository = AppDataSource.getRepository(StockTransferOrder);

    try {
      const sts = await stsRepository.findOneOrFail({ where: { id: Number(id) }, relations: ["sto", "sts_details", "sts_details.product"] });
      const sto = await stoRepository.findOneOrFail({ where: { id: sts.sto.id } })
      if (sts.status === 'ดำเนินการ') {
        //update sto
        await stoRepository.update(sto.id, {
          transfer_status: TransferStatus.CONFIRMED
        });

      } else if (sts.status === 'เตรียมรายการ') {
        let count = 0
        // แปลง sto_details → sts_details
        await Promise.all(
          sts.sts_details.map(async (detail) => {
            if (detail.sts_details_status === 'ไม่มีจำนวนส่ง') {
              count += 1;
            }
          })
        );
        //ีupate sto
        if (count > 0) {
          await stoRepository.update(sto.id, {
            transfer_status: TransferStatus.PREPARING
          });
        } else {
          await stoRepository.update(sto.id, {
            transfer_status: TransferStatus.WAITING_TO_SEND
          });
        }
      } // เดี๋ยวมาต่อ
      stsRepository.merge(sts, req.body);
      const updatedSts = await stsRepository.save(sts);
      res.status(200).json(updatedSts);
    } catch (error) {
      res.status(400).json({ message: "Error updating STS", error });
    }
  }

  public async update(req: Request, res: Response): Promise<void> { //update ใบ STS manual
    const { id } = req.params;
    const stsRepository = AppDataSource.getRepository(StockTransferSlip);
    try {
      const sts = await stsRepository.findOne({ where: { id: Number(id) } });
      if (sts) {
        stsRepository.merge(sts, req.body);
        const updatedSts = await stsRepository.save(sts);
        res.status(200).json(updatedSts);
      } else {
        res.status(404).json({ message: "STS not found" });
      }
    } catch (error) {
      res.status(400).json({ message: "Error updating STS", error });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const stsRepository = AppDataSource.getRepository(StockTransferSlip);
    try {
      const result = await stsRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "STS not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }
}