import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { SpecialReportGroup } from "../Models/SpecialReportGroup";

export class SpecialReportGroupController {
  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const specialReportGroupRepository = AppDataSource.getRepository(SpecialReportGroup);
      const groups = await specialReportGroupRepository.find();
      res.status(200).json(groups);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const specialReportGroupRepository = AppDataSource.getRepository(SpecialReportGroup);
      const group = await specialReportGroupRepository.findOne({ where: { id: Number(id) } });

      if (group) {
        res.status(200).json(group);
      } else {
        res.status(404).json({ message: "Special Report Group not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async create(req: Request, res: Response): Promise<void> {
    const specialReportGroupRepository = AppDataSource.getRepository(SpecialReportGroup);

    try {
      const newGroup = specialReportGroupRepository.create(req.body);
      const savedGroup = await specialReportGroupRepository.save(newGroup);
      res.status(201).json(savedGroup);
    } catch (error) {
      res.status(400).json({ message: "Error creating Special Report Group" });
    }
  }

  public async update(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const specialReportGroupRepository = AppDataSource.getRepository(SpecialReportGroup);

    try {
      const group = await specialReportGroupRepository.findOne({ where: { id: Number(id) } });
      if (group) {
        specialReportGroupRepository.merge(group, req.body);
        const updatedGroup = await specialReportGroupRepository.save(group);
        res.status(200).json(updatedGroup);
      } else {
        res.status(404).json({ message: "Special Report Group not found" });
      }
    } catch (error) {
      res.status(400).json({ message: "Error updating Special Report Group" });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const specialReportGroupRepository = AppDataSource.getRepository(SpecialReportGroup);

    try {
      const result = await specialReportGroupRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "Special Report Group not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }
}
