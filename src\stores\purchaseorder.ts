import { defineStore, acceptHMRUpdate } from 'pinia'
import { PurchaseOrderItemService } from 'src/services/purchaseorderItemService'
import { purchaseOrderService } from 'src/services/purchaseorderService'
import type { Product } from 'src/types/product'
import type { PurchaseOrder } from 'src/types/purchaseOrder'
import type { PurchaseOrderItems } from 'src/types/purchaseOrderitems'
import type { user } from 'src/types/user'
const defaultForm: PurchaseOrder = {
  id: 0,
  code: '',
  supplier: {
    id: 0,
    supplier_number: '',
    name: '',
    address: '',
    tel: '',
    tax_number: '',
    fax: '',
    contact_name: '',
    email: '',
    type: {
      id: 0,
      name: '',
    },
    isactive: false,
    credit_days: 0,
  },
  branch: {
    id: 0,
    name: '',
    address: '',
    contact_name: '',
    contact_phone: '',
  },
  contact: '',
  address: '',
  date: new Date(),
  user: {
    id: 0,
    password: '',
    name: '',
    image: '',
    tel: '',
    role: '',
    hour_work: 0,
    sick_level: 0,
    personal_leave: 0,
    branch: {
      id: 0,
      name: '',
      address: '',
      contact_name: '',
      contact_phone: '',
    },
  },
  po_total: 0.0,
  vat_percent: 7.0,
  tax: 7.0,
  tax_total: 0.0,
  status: '',
  order_date: new Date(),
  order_discount: 0.0,
  note: '',
  order_total: 0.0,
  receive_total: 0.0,
  product_price_tax: '',
  order_discount_tax: '',
  receive_status: '',
  purchase_order_item: [
    {
      id: 0,
      purchase_order: {
        id: 0,
        code: '',
      } as PurchaseOrder,
      product: {
        id: 0,
        product_code: '',
        product_name: '',
      } as Product,
      quantity: 0,
      unit_price: 0,
      total_price: 0,
    },
  ],
}
const defaultFormOrderItems: PurchaseOrderItems = {
  id: 0,
  product: {
    id: 0,
    product_code: '',
    product_name: '',
    generic_name: '',
    standard_cost: 0,
    selling_price: 0,
    storage_location: '',
    stock_min: 0,
    stock_max: 0,
    packing_size: '',
    reg_no: '',
    manufacturer: {
      id: 0,
      supplier_number: '',
      name: '',
      address: '',
      tel: '',
      tax_number: '',
      contact_name: '',
      fax: '',
      email: '',
      type: {
        id: 0,
        name: '',
      },
      isactive: false,
      credit_days: 0,
    },
    distributor: {
      id: 0,
      supplier_number: '',
      name: '',
      address: '',
      tel: '',
      tax_number: '',
      contact_name: '',
      fax: '',
      email: '',
      type: {
        id: 0,
        name: '',
      },
      isactive: false,
      credit_days: 0,
    },
    indications: '',
    warnings: '',
    purchase_notes: '',
    cost_notes: '',
    sales_alert_message: '',
    generic_group: '',
    product_group: {
      id: 0,
      name: '',
    },
    wholesale_control_group: '',
    special_report_group: {
      id: 0,
      name: '',
    },
    unit: '',
    barcode: '',
    isactive: false,
    wholesale: false,
    wholesale1: 0,
    wholesale2: 0,
    wholesale3: 0,
    wholesale4: 0,
    wholesale5: 0,
    retail: false,
    retail1: 0,
    retail2: 0,
    retail3: 0,
    retail4: 0,
    retail5: 0,
    manufactureDate: '',
    expirationDate: '',
  },
  quantity: 0,
  unit_price: 0,
  total_price: 0,
}
export const usePurchaseOrderStore = defineStore('purchaseOrder', {
  state: () => ({
    form: {} as PurchaseOrder,
    currentStatus: '',
    formItems: { ...defaultForm },
    formOrderItems: { ...defaultFormOrderItems },
    orders: [] as PurchaseOrder[],
    orderItems: [] as PurchaseOrderItems[],
    editOrderItems: [] as PurchaseOrderItems[],
    allProducts: [] as PurchaseOrderItems[],
    deletedIds: [] as number[],
    users: [] as user[],
  }),

  getters: {
    totalOrders: (state): number => state.orders.length,
    totalOrdersItems: (state): number => state.orderItems.length,
    getOrderById:
      (state) =>
        (id: number): PurchaseOrder | undefined =>
          state.orders.find((o) => o.id === id),
    availableProductsForDialog: (state): PurchaseOrderItems[] => {
      return state.orderItems.filter((p) => !state.deletedIds.includes(p.product.id))
    },
  },

  actions: {
    // 🔹 โหลดคำสั่งซื้อทั้งหมด
    async fetchOrders() {
      try {
        this.orders = await purchaseOrderService.getAll()
      } catch (error) {
        console.error('Error fetching orders:', error)
      }
    },

    async fetchOrdersItem() {
      if (!this.form.id) return // ถ้า id เป็น 0 ไม่ต้อง fetch
      try {
        const data = await purchaseOrderService.getPOItemByPOId(this.form.id)
        console.log('API Response:', data)
        this.orderItems = data
        this.allProducts = data
        // this.editOrderItems = JSON.parse(JSON.stringify(data));
      } catch (error) {
        console.error('Error fetching orders:', error)
      }
    },
    // 🔹 ดึงคำสั่งซื้อจาก ID
    async fetchOrderById(id: number) {
      try {
        const order = await purchaseOrderService.getById(id)
        return (this.form = order)
      } catch (error) {
        console.error(`Error fetching order ${id}:`, error)
      }
    },

    // 🔹 เพิ่มคำสั่งซื้อใหม่
    async addOrder(order: PurchaseOrder) {
      try {
        order.status = this.currentStatus
        const newOrder = await purchaseOrderService.create(order)
        this.form = newOrder
        this.orders.push(newOrder)
        console.log(this.orders)
      } catch (error) {
        console.error('Error adding order:', error)
      }
    },
    async addProduct() {
      try {
        await PurchaseOrderItemService.addProduct(this.orderItems, this.form.id)
      } catch (error) {
        console.error('Error adding order:', error)
      }
    },

    // 🔹 อัปเดตคำสั่งซื้อ
    async updateOrder(updatedOrder: PurchaseOrder) {
      try {
        updatedOrder.status = this.currentStatus
        const updated = await purchaseOrderService.update(updatedOrder.id, updatedOrder)
        const index = this.orders.findIndex((o) => o.id === updated.id)
        if (index !== -1) this.orders[index] = updated
      } catch (error) {
        console.error(`Error updating order ${updatedOrder.id}:`, error)
      }
    },
    async removeOrderItem() {
      try {
        await PurchaseOrderItemService.deletePoItems(this.deletedIds)
      } catch (error) {
        console.error(`Error deleting order:`, error)
      }
    },
    // 🔹 ลบคำสั่งซื้อ
    async removeOrder(orderId: number) {
      try {
        await purchaseOrderService.delete(orderId)
        this.orders = this.orders.filter((o) => o.id !== orderId)
      } catch (error) {
        console.error(`Error deleting order ${orderId}:`, error)
      }
    },

    // 🔹 รีเซ็ตฟอร์ม
    resetForm() {
      this.form = {} as PurchaseOrder
    },
    resetFormItem() {
      this.deletedIds = []
    },
    resetFormOrderItems() {
      this.formOrderItems = structuredClone(defaultFormOrderItems)
    },

    async filterOrders(search: string, filter: string, startDate: string, endDate: string) {
      try {
        const filteredOrders = await purchaseOrderService.filter(search, filter, startDate, endDate)
        this.orders = filteredOrders
      } catch (error) {
        console.error('Error filtering orders:', error)
      }
    },
  },
})

// HMR Support (Hot Module Replacement)
if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(usePurchaseOrderStore, import.meta.hot))
}
