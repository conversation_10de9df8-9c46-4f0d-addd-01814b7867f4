import { Request, Response } from "express";
import { AppDataSource } from "../../Config/db";
import { StockTransferOrder } from "../../Models/StockTransferOrder";
import { StockTransferSlip } from "../../Models/StockTransferSlip";

export class TransferDashboardController {
  // GET /api/transfer/volume-by-branch
  public async getVolumeByBranch(req: Request, res: Response): Promise<void> {
    try {
      const stsRepository = AppDataSource.getRepository(StockTransferSlip);
      
      const result = await AppDataSource
  .createQueryBuilder()
  .select([
    "source.name AS source_branch",
    "destination.name AS destination_branch",
    "SUM(details.sts_quantity) AS total_transferred",
    "COUNT(DISTINCT sts.id) AS transfer_count"
  ])
  .from("stock_transfer_slip", "sts")
  .leftJoin("stock_transfer_slip_details", "details", "details.stsId = sts.id")
  .leftJoin("branch", "source", "source.id = sts.sourceBranchId")
  .leftJoin("branch", "destination", "destination.id = sts.destinationBranchId")
  .groupBy("source.id, destination.id")
  .orderBy("total_transferred", "DESC")
  .getRawMany();



      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  // GET /api/transfer/top-issued-products
  public async getTopIssuedProducts(req: Request, res: Response): Promise<void> {
    try {
      const stsRepository = AppDataSource.getRepository(StockTransferSlip);
      
      const result = await AppDataSource
  .createQueryBuilder()
  .select([
    "product.id AS product_id",
    "product.product_code AS product_code",
    "product.product_name AS product_name",
    "SUM(details.sts_quantity) AS total_transferred",
    "COUNT(DISTINCT sts.id) AS transfer_frequency"
  ])
  .from("stock_transfer_slip", "sts")
  .leftJoin("stock_transfer_slip_details", "details", "details.stsId = sts.id")
  .leftJoin("product", "product", "product.id = details.productId")
  .groupBy("product.id")
  .orderBy("total_transferred", "DESC")
  .limit(20)
  .getRawMany();



      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  // GET /api/transfer/flow-map
  public async getFlowMap(req: Request, res: Response): Promise<void> {
    try {
      const stsRepository = AppDataSource.getRepository(StockTransferSlip);
      
      const result = await AppDataSource
  .createQueryBuilder()
  .select([
    "source_branch.id AS source_id",
    "source_branch.name AS source_name",
    "destination_branch.id AS destination_id",
    "destination_branch.name AS destination_name",
    "SUM(details.sts_quantity) AS flow_quantity",
    "COUNT(DISTINCT sts.id) AS flow_count"
  ])
  .from("stock_transfer_slip", "sts")
  .leftJoin("stock_transfer_slip_details", "details", "details.stsId = sts.id")
  .leftJoin("branch", "source_branch", "source_branch.id = sts.sourceBranchId")
  .leftJoin("branch", "destination_branch", "destination_branch.id = sts.destinationBranchId")
  .groupBy("source_branch.id, destination_branch.id")
  .getRawMany();



      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }
}