<template>
  <q-dialog v-model="isOpen" maximized>
    <q-card style="max-width: 1100px">
      <q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">ใบรายการสั่งซื้อสินค้า</div>
          <q-btn icon="close" @click="closeDialog()" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section style="overflow: auto; max-height: 80vh">
        <div class="col-lg-12 row">
          <div class="col-12 col-md-4 q-mt-sm order-1 order-md-1">
            <div class="gap-container">
              <div class="text-white shadow-2 container-headerhalf full-width row items-center">
                รหัสใบ PO
              </div>
              <div class="shadow-2 containerhalf full-width">
                <div v-if="store.form" class="text-po">
                  {{ store.form.code }}
                </div>
              </div>
            </div>
          </div>
          <div class="col-12 col-md-8 q-mt-sm order-2 order-md-1">
            <div class="gap-container">
              <div class="gap-container-left">
                <div class="text-white shadow-2 container-headerhalf2 full-width row items-center">
                  <span>เปลี่ยนสถานะสินค้า</span>
                  <span class="status-text">สถานะสินค้า : {{ store.currentStatus }}</span>
                </div>
                <div class="shadow-2 containerhalf2 flex-container full-width">
                  <div class="row q-col-gutter-sm">
                    <q-radio v-model="store.currentStatus" val="เตรียมรายการ" label="เตรียมรายการ" color="orange"
                      class="custom-radio" />
                    <q-radio v-model="store.currentStatus" val="ดำเนินการ" label="ดำเนินการ" color="blue"
                      class="custom-radio" />
                    <q-radio v-model="store.currentStatus" val="เสร็จสมบูรณ์" label="เสร็จสมบูรณ์" color="green"
                      class="custom-radio" />
                    <q-radio v-model="store.currentStatus" val="ยกเลิก" label="ยกเลิก" color="red"
                      class="custom-radio" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- รายละเอียดบริษัท -->
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            บริษัทจำหน่ายสินค้า
          </div>
          <div class="shadow-2 container">
            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-1 q-mt-md" style="margin-left: 20px">บริษัท</div>
              <div class="col-12 col-md-10">
                <q-select class="input-container col-9" v-model="store.form.supplier" :options="supplierStore.suppliers"
                  option-label="name" option-value="id" dense borderless map-options />
              </div>
            </div>

            <div class="row q-col-gutter-md" style="margin-top: 10px">
              <div class="col-12 col-md-1 q-mt-md" style="margin-left: 20px">ชื่อผู้ติดต่อ</div>
              <div class="col-12 col-md-4">
                <q-input class="input-container" v-model="store.form.contact" dense borderless type="text"
                  style="width: 100%" />
              </div>
              <div class="col-12 col-md-1 q-mt-md">ที่อยู่</div>
              <div class="col-12 col-md-5">
                <q-input class="input-container" v-model="store.form.address" dense borderless type="textarea"
                  style="width: 100%" />
              </div>
            </div>
          </div>
        </div>
        <!-- การสั่งซื้อ -->
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            รายการสั่งซื้อสินค้า
          </div>
          <div class="shadow-2 container">
            <div class="row justify-between">
              <div class="col-11 row q-my-sm">
                <div class="col-1 q-mt-md q-pr-md">เลขที่</div>
                <q-input dense borderless class="input-container" v-model="store.form.id"
                  style="width: 224px; height: 40px" readonly />
                <div class="col-1 q-pr-md flex flex-center" style="margin-left: 45px">สถานะ</div>
                <div class="row q-col-gutter-sm">
                  <q-radio keep-color v-model="store.form.status" val="เตรียมรายการ" label="เตรียมรายการ" color="orange"
                    style="color: orange" size="sm" />
                  <q-radio keep-color v-model="store.form.status" val="ดำเนินการ" label="ดำเนินการ" color="blue"
                    style="color: royalblue" size="sm" />
                  <q-radio keep-color v-model="store.form.status" val="เสร็จสมบูรณ์" label="เสร็จสมบูรณ์" color="green"
                    style="color: green" size="sm" />
                  <q-radio keep-color v-model="store.form.status" val="ยกเลิก" label="ยกเลิก" color="red"
                    style="color: red" size="sm" />
                </div>
              </div>
              <div class="col-3 width-column">
                <div class="row items-center q-mb-sm" style="margin-top: 10px" justify-content: space-between>
                  <div class="col-3 q-mt-sm q-pr-md">วันที่</div>
                  <q-input dense borderless class="input-container col-9" v-model="formattedDate" readonly />

                  <div class="col-12 row q-col-gutter-ms" style="margin-top: 10px">
                    <div class="col-3 q-mt-sm">พนักงาน</div>
                    <q-select class="input-container col-9" v-model="store.form.user" :options="userStore.users"
                      option-label="name" option-value="id" dense borderless map-options />
                  </div>

                  <div class="col-lg-12 row" style="margin-top: 10px">
                    <div class="col-3 q-mt-sm q-pr-md">รวมเงิน</div>
                    <q-input dense borderless class="input-container col-9" v-model="totalPrice"
                      :rules="[(val) => /^\d*$/.test(val)]" style="height: 40px" />
                    <div class="col-12 row" style="margin-top: 10px">
                      <div class="col-3 q-mt-sm q-pr-md">ภาษี (%)</div>
                      <q-input dense borderless class="input-container col-3" v-model="store.form.vat_percent"
                        type="number" />

                      <div class="col-3 q-mt-sm q-pr-md text-center">เงินภาษี</div>
                      <q-input dense borderless class="input-container-2 col-3" v-model="tax" type="number" />
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-3 width-column">
                <div class="col-12 row" style="margin-top: 10px">
                  <div class="col-4 q-mt-sm q-pr-md">วันที่สั่งสินค้า</div>
                  <div class="col-12 col-md-8">
                    <q-input dense borderless class="input-container" v-model="formattedOrderDate" style="width: 100%">
                      <q-icon name="event" size="md" color="black" style="cursor: pointer; margin-top: 5px">
                        <q-popup-proxy transition-show="scale" transition-hide="scale">
                          <q-date v-model="formattedOrderDate" mask="DD/MM/YYYY" color="teal" />
                        </q-popup-proxy>
                      </q-icon>
                    </q-input>
                  </div>
                </div>
                <div class="col-12 row" style="margin-top: 10px">
                  <div class="col-4 q-mt-md q-pr-md">หมายเหตุ</div>
                  <div class="col-12 col-md-8">
                    <q-input dense borderless class="input-container" v-model="store.form.note"
                      style="width: 100%; height: 90px" type="text" />
                  </div>
                </div>
                <div class="col-12 row" style="margin-top: 10px">
                  <div class="col-4 q-mt-sm q-pr-md">รวมจำนวน</div>
                  <div class="col-4 col-md-4">
                    <q-input dense borderless class="input-container" v-model="store.form.order_total" label="สั่ง"
                      style="width: 100%" type="number" />
                  </div>

                  <div class="col-4 col-md-4">
                    <q-input dense borderless class="input-container" v-model="store.form.receive_total" label="รับ"
                      style="margin-left: 2px; width: 100%" type="number" />
                  </div>
                </div>
                <div class="col-12 row" style="margin-top: 10px">
                  <div class="col-12 q-mt-sm q-pr-md">
                    <div class="col-3 mini-container width-column" style="width: 100%; margin-left: 10px">
                      <div class="row items-center">
                        <div class="col-4 q-mt-sm q-pr-md">รับสินค้า</div>
                        <div class="col-4 col-md-12">
                          <q-radio keep-color v-model="store.form.receive_status" val="รอรับ" label="รอรับ"
                            style="font-size: 12px; color: orange" size="sm" color="orange" />
                          <q-radio keep-color v-model="store.form.receive_status" val="รับครบแล้ว" label="รับครบแล้ว"
                            style="font-size: 12px; color: green" size="sm" color="green" />
                          <q-radio keep-color v-model="store.form.receive_status" val="ยกเลิกรับ" label="ยกเลิกรับ"
                            style="font-size: 12px; color: red" size="sm" color="red" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-1 width-column">
                <div class="row">
                  <div class="col-xl-1" style="font-size: 12px">ส่วนลดท้ายบิล</div>
                  <div class="col-1 mini-container width-column">
                    <div class="col-2">
                      <div class="row items-center">
                        <div style="font-size: 13px; margin-left: 10px">จำนวนส่วนลด</div>
                        <q-input dense borderless class="input-container-v3" v-model="store.form.order_discount"
                          style="margin-left: 10px" type="number" />
                      </div>
                    </div>
                  </div>
                  <div class="col-12 row" style="margin-bottom: 20px">
                    <div class="col-4 q-mt-sm q-pr-md" style="font-size: 12px; margin-top: 5px">
                      สินค้ามีภาษี
                    </div>
                    <div class="col-3 mini-container width-column">
                      <div class="col-4" style="font-size: 13px">
                        <div class="col-xl-2 q-mt-md">ราคาสินค้า</div>
                        <div class="col-12 col-md-8 text-center">
                          <q-radio v-model="store.form.product_price_tax" val="รวมภาษี" label="รวมภาษี"
                            color="grey-7" />
                          <q-radio v-model="store.form.product_price_tax" val="ไม่รวมภาษี" label="ไม่รวมภาษี"
                            color="grey-7" />
                        </div>
                        <div class="col-4 q-mt-sm q-pr-md">ส่วนลดท้ายบิล</div>
                        <div class="col-12 col-md-8 text-center" style="margin-bottom: 10px">
                          <q-radio v-model="store.form.order_discount_tax" val="ก่อนภาษี" label="ก่อนภาษี"
                            color="grey-7" />
                          <q-radio v-model="store.form.order_discount_tax" val="หลังภาษี" label="หลังภาษี"
                            color="grey-7" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!--รายการสินค้า-->
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            รายการสินค้า
            <q-btn class="btn-add" label="เพิ่มสินค้า" @click="openAddProductDialog"></q-btn>
          </div>
          <div class="shadow-2 container-table">
            <q-table flat class="body-table" :rows="store.orderItems" :columns="columns" row-key="id">
              <template v-slot:body-cell-actions="props">
                <q-td :props="props" class="q-gutter-x-sm" style="min-width: 100px">
                  <q-btn icon="edit" padding="none" flat style="color: #e19f62" @click="editPoDetails(props.row)" />
                  <q-btn icon="delete" padding="none" flat style="color: #b53638" @click="deletePoDetails(props.row)" />
                </q-td>
              </template>
              <template v-slot:body-cell-index="props">
                <q-td :props="props">
                  {{ props.rowIndex + 1 }}
                </q-td>
              </template>
              <template v-slot:bottom-row>
                <q-tr class="total-row">
                  <!-- ให้ "รวม" อยู่ทางขวาโดยใช้ text-right -->
                  <q-td colspan="5" class="text-right total-label justify-end">รวม</q-td>

                  <!-- ให้ตัวเลขอยู่ชิดขวาสุดของตาราง -->
                  <q-td class="text-right total-value">
                    <div class="row justify-end items-center no-wrap">
                      <span>{{ totalPrice }}</span>
                      <span class="q-ml-sm">บาท</span>
                    </div>
                  </q-td>
                </q-tr>
              </template>
            </q-table>
          </div>
        </div>
      </q-card-section>
      <q-card-actions align="right">
        <q-btn class="btn-accept-details" dense flat label="บันทึก" @click="saveDialog" />

        <q-btn class="btn-print" dense flat label="พิมพ์เอกสาร" @click="closeDialog" />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <!-- Sub Dialogs -->
  <AddProductToPODetail v-model="addProductDialogOpen" @product-added="onProductAdded" />
  <EditPOItemsDialog v-model="editPOItemsDialogOpen" :po-item="selectedPOItem" :mode="editPOItemsMode"
    @item-updated="onItemUpdated" />
</template>

<script setup lang="ts">
import { usePurchaseOrderStore } from 'src/stores/purchaseorder'
import { useSupplierStore } from 'src/stores/supplier'
import { computed, nextTick, onMounted, ref } from 'vue'
import type { QTableColumn } from 'quasar'
import { date } from 'quasar' // ใช้ quasar date utility หากต้องการจัดการวันที่
import { useDialogPODetails } from 'src/stores/dialog-po-details'
import type { PurchaseOrderItems } from 'src/types/purchaseOrderitems'
import type { PurchaseOrder } from 'src/types/purchaseOrder'
import AddProductToPODetail from './addProductToPODetail.vue'
import { watch } from 'vue'
import EditPOItemsDialog from './EditPOItemsDialog.vue'
import { useStockStore } from 'src/stores/stock'
import { useProductStore } from 'src/stores/product'
import { useUserStore } from 'src/stores/userStore'
import { useBranchStore } from 'src/stores/branch'

// Props and emits for v-model support
interface Props {
  modelValue: boolean
  poData?: PurchaseOrder | null
  mode?: string
}

const props = withDefaults(defineProps<Props>(), {
  poData: null,
  mode: 'view'
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'po-updated': []
}>()

// Computed property for v-model
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const dialogPODetails = useDialogPODetails()
const store = usePurchaseOrderStore()
const supplierStore = useSupplierStore()
const stockStore = useStockStore()
const product = useProductStore()

// Dialog state for sub dialogs
const addProductDialogOpen = ref(false)
const editPOItemsDialogOpen = ref(false)
const selectedPOItem = ref<PurchaseOrderItems | null>(null)
const editPOItemsMode = ref('add')
onMounted(async () => {
  console.log(store.form)
  await store.fetchOrders()
  await store.fetchOrdersItem()
  store.currentStatus = store.form.status
  await stockStore.fetchAllStock()
  await product.fetchProducts()
  await supplierStore.loadSuppliers()
  await product.fetchProductGroups()
  await product.fetchSpecialReportGroups()
  await userStore.fetchUsers()
  await branchStore.fetchAllBranch()
  if (!store.form.date) {
    store.form.date = new Date(); // กำหนดค่าเริ่มต้นถ้ายังไม่มี
  }
  if (!store.form.order_date) {
    store.form.order_date = new Date(); // กำหนดค่าเริ่มต้นถ้ายังไม่มี
  }
})
watch(
  () => dialogPODetails.modeProduct,
  async (newMode) => {
    if (newMode === 'addProduct') {
      store.orderItems = []; // รีเซ็ตค่า orderItems

      // ใช้ nextTick เพื่อให้แน่ใจว่า DOM และการเปลี่ยนแปลงเสร็จสิ้นก่อนที่จะเรียก fetch
      await nextTick(async () => {
        console.log('nextTick')
        console.log(store.form)
        console.log(store.form.id)
        await store.fetchOrders()
        await store.fetchOrdersItem()
        store.currentStatus = store.form.status;
        await stockStore.fetchAllStock()
        await product.fetchProducts()
        await supplierStore.loadSuppliers()
        await product.fetchProductGroups()
        await product.fetchSpecialReportGroups()
        await userStore.fetchUsers()// เรียก fetchOrdersItem
        await branchStore.fetchAllBranch()
      });
    }
  }
);

watch(
  () => store.form.supplier,
  (newSupplier) => {
    if (newSupplier) {
      store.form.contact = newSupplier.contact_name || '';
      store.form.address = newSupplier.address || ''
    }
  },
  { deep: true }
);
const totalPrice = computed(() => {
  const sum = store.orderItems.reduce((sum, item) => sum + item.total_price, 0);
  return Math.round(sum * 10000) / 10000
});
const taxTotal = computed(() => {
  if (store.form.product_price_tax === 'ไม่รวมภาษี') {
    return totalPrice.value
  } else {
    return totalPrice.value + (totalPrice.value * 0.07)
  }
});
const tax = computed(() => {
  if (store.form.product_price_tax === 'ไม่รวมภาษี') {
    return 0
  } else {
    return (totalPrice.value * 7) / 100; // คำนวณภาษี 7% ของราคารวมภาษีแล้วแสดงผลทศนิยมสองตำแหน่ง
  }
});
// const orderTotal = computed(() => {

// });

const columns = <QTableColumn[]>[
  {
    name: 'index',
    label: 'ลำดับ',
    field: '',
    align: 'left' as const,
    sortable: false,
  },
  {
    name: 'product_name',
    label: 'ชื่อสินค้า',
    field: (row) => (row.product ? row.product.product_name : ''),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'quantity',
    label: 'จำนวนที่สั่ง',
    field: (row) => row.quantity,
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'unit_price',
    label: 'ราคา',
    field: (row) => row.unit_price.toFixed(4),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'total_price',
    label: 'รวมเงิน',
    field: (row) => row.total_price.toFixed(4),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'actions',
    label: '',
    align: 'center' as const,
    field: 'actions',
    sortable: false,
  },
]

function resetOrderItems() {
  store.orderItems = JSON.parse(JSON.stringify(store.orderItems)) // Deep Copy
  console.log('Reset orderItems:', store.orderItems)
}
// Dialog functions for sub dialogs
const openAddProductDialog = () => {
  console.log('Open Add product');
  store.resetFormOrderItems()
  if (props.mode === 'add') {
    console.log('halo')
    store.editOrderItems = JSON.parse(JSON.stringify(store.orderItems));
  }
  addProductDialogOpen.value = true
}

const onProductAdded = () => {
  // Handle product added event
  // await store.fetchOrdersItem()
  console.log('Product added successfully')
}
// function addProduct() {
//   // รีเซ็ตข้อมูลสินค้าใหม่ก่อนเปิด Dialog
//   console.log('Open Add product');
//   store.resetFormOrderItems()
//   dialogPODetails.openProduct('add');
//   console.log(store.orderItems);
//   store.editOrderItems = JSON.parse(JSON.stringify(store.orderItems));
// }


const closeDialog = async () => {
  resetOrderItems()
  store.resetForm()
  store.resetFormItem()
  store.editOrderItems = []
  console.log(store.deletedIds)
  dialogPODetails.mode = ''
  isOpen.value = false
  await store.fetchOrders()
}
const branchStore = useBranchStore()
const saveOrder = async () => {
  try {
    if (store.deletedIds.length > 0) {
      console.log('delete')
      await store.removeOrderItem()
      store.resetFormItem()
    }
    const poId = store.form.id
    if (!poId) {
      console.log('Adding new PO...')
      // store.form.branch = branchStore.branch
      await store.addOrder(store.form)
      await store.addProduct()
    } else {
      console.log('Updating existing PO...')
      await store.updateOrder(store.form)
      await store.addProduct()
    }
    store.editOrderItems = []

    // อัปเดตรายการสินค้า (poItem)
    // if (store.formItems && store.formItems.length > 0) {
    //   console.log("Saving PO items...");
    //   await store.saveOrderItems(poId, store.form.items);
    // }

    return true
  } catch (error) {
    console.error('Error saving order:', error)
    return false
  }
}

const saveDialog = async () => {
  store.form.po_total = totalPrice.value
  store.form.tax = tax.value
  store.form.tax_total = taxTotal.value
  const success = await saveOrder()
  store.editOrderItems = []
  if (success) {
    if (props.mode === 'add') {
      store.resetForm()
    }

    // Emit the po-updated event
    emit('po-updated')

    // Close this dialog
    isOpen.value = false

    await store.fetchOrders()
  }
  store.resetForm()
}
const editPoDetails = (row: PurchaseOrderItems) => {
  selectedPOItem.value = { ...row }
  store.formOrderItems = Object.assign({}, store.formOrderItems, row);
  editPOItemsMode.value = 'edit'
  editPOItemsDialogOpen.value = true
}

const onItemUpdated = () => {
  // Handle item updated event
  // await store.fetchOrdersItem()
  console.log('Item updated successfully')
}

function deletePoDetails(row: PurchaseOrderItems) {
  const index = store.orderItems.findIndex(item => item.product.id === row.product.id);
  if (index !== -1) {
    store.orderItems.splice(index, 1);

    if (!store.deletedIds.includes(row.product.id)) {
      store.deletedIds.push(row.product.id); // mark ว่าเคยลบ
    }
  }

  // ไม่จำเป็นต้องแก้ editOrderItems ตรงนี้เพราะ dialog จะโหลดใหม่ทุกครั้ง

  console.log('Deleted IDs:', store.deletedIds);
  console.log('Remaining items:', store.orderItems.length);
}


const formattedOrderDate = computed({
  get() {
    const dateValue = store.form.order_date ? store.form.order_date : new Date()
    return date.formatDate(dateValue, 'DD/MM/YYYY')
  },
  set(value: string) {
    const parts = value.split('/')
    if (parts.length === 3) {
      const [day, month, year] = parts
      const newDate = new Date(`${year}-${month}-${day}T00:00:00`)
      store.form.order_date = newDate // ✅ เก็บเป็น Date ตาม type
    } else {
      store.form.order_date = new Date()
    }
  },
})


// const formattedDate = computed(() => {
//   // ตรวจสอบให้แน่ใจว่า store.form.date เป็น Date ที่ถูกต้อง
//   let dateValue = store.form.date;
//   dateValue = new Date()

//   if (!dateValue) {
//     console.error("❌ date is undefined or null");
//     return "00/00/0000"; // ค่าเริ่มต้นป้องกัน error
//   }

//   // แปลง store.form.date เป็น Date หากยังเป็น string
//   const parsedDate = typeof dateValue === "string" ? new Date(dateValue) : dateValue;

//   // ตรวจสอบว่า parsedDate เป็น Date และเป็นวันที่ที่ถูกต้อง
//   if (!(parsedDate instanceof Date) || isNaN(parsedDate.getTime())) {
//     // console.error(`❌ Invalid date format: ${dateValue}`);
//     return "00/00/0000"; // หรือใช้ค่าปัจจุบันแทน
//   }

//   // ใช้ date.formatDate หรือวิธีแปลงอื่นๆ ในการแสดงผล
//   return date.formatDate(parsedDate, 'DD/MM/YYYY');
// });
const formattedDate = computed({
  get() {
    return date.formatDate(store.form.date, 'DD/MM/YYYY') // แปลงจาก Date เป็น string ในรูปแบบ YYYY-MM-DD
  },
  set(value: string) {
    const newDate = new Date(value) // แปลงจาก string กลับเป็น Date
    store.form.date = newDate // อัพเดทค่าใน store
  },
})
const userStore = useUserStore()
// const users = computed(() =>
//   userStore.users.map((supplier) => ({
//     id: supplier.id,
//     label: supplier.name,
//   })),
// )
</script>

<style scoped>
.text-po {
  margin-left: 15px;
  font-size: 30px;
}

.total-row {
  background-color: #83a7d8;
  color: black;
  font-weight: bold;
}

/* ทำให้ "รวม" อยู่ชิดขวาสุดของคอลัมน์ก่อนตัวเลข */
.total-label {
  text-align: right;
}

/* ทำให้ตัวเลขอยู่ชิดขวาสุดของเซลล์ */
.total-value {
  text-align: right;
  padding-right: 16px;
  /* ปรับระยะห่างให้ดูดี */
}

.total-row td {
  text-align: right;
  /* จัดข้อความให้ชิดขวาทุกช่อง */
  padding: 12px;
  /* ปรับระยะห่างให้ดูดีขึ้น */
}

.container-table {
  background-color: #294888;
  padding: 0;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  overflow: hidden;
}

.container-header-table {
  background-color: #294888;
  /* สีของหัวข้อ */
  color: white;
  padding: 16px;
  font-size: 16px;
  font-weight: bold;
}

.body-table {
  background-color: #deecff;
}

:deep(.q-table thead tr) {
  background-color: #83a7d8;
}

.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.containerhalf {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  width: 350px;
  height: 100px;
}

.containerhalf2 {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  width: 680px;
  height: 100px;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.container-headerhalf {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  width: 350px;
  height: 55px;
}

.container-headerhalf2 {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  width: 680px;
  height: 55px;
}

.mini-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container-2 {
  padding-left: 5px;
  padding-right: 5px;
  background-color: white;
  border-radius: 5px;
}

.btn-add {
  background-color: #ffffff;
  color: #000000;
  border-radius: 5px;
  margin-left: 20px;
  font-size: 13px;
}

.btn-accept {
  background-color: #36b54d;
  width: 150px;
  height: 40px;
  border-radius: 10px;
  margin-right: 10px;
}

.btn-print {
  background-color: #83a7d8;
  width: 150px;
  height: 40px;
  border-radius: 10px;
  margin-right: 10px;
}

.gap-container {
  margin-bottom: 20px;
}

.gap-container-left {
  margin-left: 20px;
}

.dialog-container {
  border-radius: 10px;
}

.input-container-v2 {
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  background-color: #83a7d8;
}

.input-container-v3 {
  padding-left: 10px;
  padding-right: 10px;
  margin-top: 5px;
  margin-bottom: 5px;
  border-radius: 5px;
  background-color: #d6e4f6;
  width: 150px;
}

.width-column {
  width: 300px;
}

.column-table {
  height: 40px;
  background-color: #294888;
  color: white;
}

.column-table-2 {
  height: 40px;
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border: 2px solid #294888;
}

/* .custom-radio {
  padding: 10px;
  color: white !important;
} */

.flex-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  /* ปรับระยะห่างระหว่าง radio */
  padding: 10px;
}

.status-text {
  margin-left: 300px;
  margin-right: 30px;
}

.custom-radio .q-radio__inner--truthy {
  background-color: currentColor !important;
  opacity: 1;
}

.custom-radio .q-radio__bg {
  color: currentColor !important;
  /* เปลี่ยนสีขอบให้ตรงกับ radio */
}

.custom-radio .q-radio__label {
  color: black !important;
  /* สีตัวอักษร */
  font-weight: bold;
  margin-left: 5px;
}
</style>
