import { Router } from "express";
import { WorkingScheduleController } from "../Controllers/User/WorkingSchedule";

const router = Router();
const controller = new WorkingScheduleController();

// Get working schedule for a specific user
router.get("/user/:userId", (req, res) => 
  controller.getUserSchedule(req, res)
);

// Create or update working schedule
router.post("/", (req, res) => 
  controller.createOrUpdateSchedule(req, res)
);

// Update working schedule
router.put("/", (req, res) => 
  controller.createOrUpdateSchedule(req, res)
);

// Get all working schedules
router.get("/", (req, res) => 
  controller.getAllSchedules(req, res)
);

// Delete working schedule
router.delete("/:scheduleId", (req, res) => 
  controller.deleteSchedule(req, res)
);

export default router;
