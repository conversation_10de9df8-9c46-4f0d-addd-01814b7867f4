import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { PurchaseOrderItem } from "../Models/PurchaseOrderItem";
import { Product } from "../Models/Product";
import { In } from "typeorm";
import { StockTransferSlipDetails } from "../Models/StockTransferSlipDetails";
import { StockTransferSlip } from "../Models/StockTransferSlip";
import { StockTransferSlipLotDetails } from "../Models/StockTransferSlipLotDetails";
import { LotStatus } from "../enums/lot-status";

export class StockTransferSlipLotDetailsController {
  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const lotDetailsRepository = AppDataSource.getRepository(StockTransferSlipLotDetails);
      const lotDetails = await lotDetailsRepository.find({ relations: ["stock", "stock.product", "sts_detail"] });
      res.status(200).json(lotDetails);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const lotDetailsRepository = AppDataSource.getRepository(StockTransferSlipLotDetails);
      const lotDetails = await lotDetailsRepository.findOne({
        where: { id: Number(id) },
        relations: ["stock", "stock.product", "sts_detail"]
      });
      if (lotDetails) {
        res.status(200).json(lotDetails);
      } else {
        res.status(404).json({ message: "Lotdetails not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getBySTS(req: Request, res: Response): Promise<void> {
    const { stsId } = req.params;
    try {
      const lotDetailsRepository = AppDataSource.getRepository(StockTransferSlipLotDetails);
      const lotDetails = await lotDetailsRepository.find({
        where: { sts_detail: { sts: { id: Number(stsId) } } },
        relations: ["stock", "stock.product", "sts_detail"]
      });
      if (lotDetails) {
        res.status(200).json(lotDetails);
      } else {
        res.status(404).json({ message: "Lot details not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async addLotsToSlipItem(req: Request, res: Response): Promise<void> {
    const stsDetailsRepository = AppDataSource.getRepository(StockTransferSlipDetails);
    const lotDetailsRepository = AppDataSource.getRepository(StockTransferSlipLotDetails);
    const stsRepository = AppDataSource.getRepository(StockTransferSlip);

    try {
      const stsId = parseInt(req.params.stsId, 10);
      const stsDetailsId = parseInt(req.params.stsDetailsId, 10);

      if (isNaN(stsId) || isNaN(stsDetailsId)) {
        res.status(400).json({ message: "Invalid stsId or stsDetailsId" });
        return;
      }

      const lotDetails = req.body;

      if (!Array.isArray(lotDetails)) {
        res.status(400).json({ message: "Invalid request body" });
        return;
      }

      const sts = await stsRepository.findOne({
        where: { id: stsId },
        relations: ["source_branch", "destination_branch"]
      });

      if (!sts) {
        res.status(404).json({ message: "Stock Transfer Slip not found" });
        return;
      }

      if (sts?.status !== "เตรียมรายการ") {
        res.status(400).json({ message: "STS must be in 'เตรียมรายการ' status" });
        return;
      }

      // หา sts_detail ที่จะ update
      const stsDetail = await stsDetailsRepository.findOneOrFail({
        where: { id: stsDetailsId },
        relations: ["lot_details"]
      });

      // สร้าง lot ใหม่และผูกกับ sts_detail
      const newLotEntities = lotDetailsRepository.create(
        lotDetails.map(lot => ({
          ...lot,
          sts_detail: stsDetail
        }))
      );

      // รวมจำนวน lot ทั้งหมดที่จะเพิ่ม
      const totalQty = newLotEntities.reduce((sum, lot) => sum + lot.quantity_ordered, 0);

      // อัปเดต sts_detail
      stsDetail.total_quantity_ordered += totalQty;
      stsDetail.total_quantity_sent += totalQty;

      await stsDetailsRepository.save(stsDetail);

      // save lot
      const savedLots = await lotDetailsRepository.save(newLotEntities);

      res.status(201).json(savedLots);

    } catch (error) {
      console.error("Error adding lots to STS detail:", error);
      res.status(500).json({ message: "Internal server error", error });
    }
  }



  public async update(req: Request, res: Response): Promise<void> {
    const { lotDetailsId, stsDetailsId } = req.params;
    const lotDetailsRepository = AppDataSource.getRepository(StockTransferSlipLotDetails);
    const stsDetailsRepository = AppDataSource.getRepository(StockTransferSlipDetails);

    try {
      const lotDetail = await lotDetailsRepository.findOne({ where: { id: Number(lotDetailsId) }, relations: ["sts_detail"] });
      if (!lotDetail) {
        res.status(404).json({ message: "Lot Details not found" });
        return;
      }

      // อัปเดตข้อมูลใน lotDetail ตัวที่ต้องการ
      lotDetailsRepository.merge(lotDetail, req.body);
      await lotDetailsRepository.save(lotDetail); // ต้อง save ก่อนถึงจะคำนวณได้ถูกต้อง

      // ดึง lot ทั้งหมดใน sts_detail นั้น
      const allLotDetails = await lotDetailsRepository.find({
        where: { sts_detail: { id: Number(stsDetailsId) } },
      });

      // คำนวณผลรวมจากรายการทั้งหมด
      const totalOrdered = allLotDetails.reduce((sum, item) => sum + item.quantity_ordered, 0);
      const totalSent = allLotDetails.reduce((sum, item) => sum + item.quantity_sent, 0);

      // อัปเดตไปที่ sts_detail
      const stsDetail = await stsDetailsRepository.findOneOrFail({ where: { id: Number(stsDetailsId) } });
      stsDetail.total_quantity_ordered = totalOrdered;
      stsDetail.total_quantity_sent = totalSent;
      await stsDetailsRepository.save(stsDetail);

      res.status(200).json(lotDetail);
    } catch (error) {
      res.status(400).json({ message: "Error updating Lot Details", error });
    }
  }


  public async autoFillQuantitySent(req: Request, res: Response): Promise<void> {
    const lotDetailsRepository = AppDataSource.getRepository(StockTransferSlipLotDetails);
    const stsDetailsRepository = AppDataSource.getRepository(StockTransferSlipDetails);
    const { stsDetailId } = req.params;

    try {
      const lots = await lotDetailsRepository.find({
        where: { sts_detail: { id: Number(stsDetailId) } },
        relations: ["sts_detail", "sts_detail.sts", "stock"] // <-- ต้อง join กับ stock
      });

      if (!lots.length) {
        res.status(404).json({ message: "No lot details found." });
        return;
      }

      const updatedLots = lots.map((lot) => {
        const stockQty = lot.stock?.remaining || 0;
        const ordered = lot.quantity_ordered;
        let sentQty = 0;
        let status: LotStatus;

        if (stockQty === 0) {
          sentQty = 0;
          status = LotStatus.NONE;
        } else if (stockQty >= ordered) {
          sentQty = ordered;
          status = LotStatus.MATCHED;
        } else {
          sentQty = stockQty;
          status = LotStatus.LESS;
        }

        lot.quantity_sent = sentQty;
        lot.lot_status = status;
        return lot;
      });

      await lotDetailsRepository.save(updatedLots);

      // ✅ อัปเดต total ใน sts_detail
      const totalOrdered = updatedLots.reduce((sum, lot) => sum + lot.quantity_ordered, 0);
      const totalSent = updatedLots.reduce((sum, lot) => sum + lot.quantity_sent, 0);

      const stsDetail = await stsDetailsRepository.findOneOrFail({ where: { id: Number(stsDetailId) } });
      stsDetail.total_quantity_ordered = totalOrdered;
      stsDetail.total_quantity_sent = totalSent;
      await stsDetailsRepository.save(stsDetail);

      res.status(200).json({ message: "Auto-filled quantity_sent successfully", updatedLots });
    } catch (error) {
      res.status(500).json({ message: "Failed to auto-fill", error });
    }
  }
  // public async update(req: Request, res: Response): Promise<void> {
  //   const { id } = req.params;
  //   const stsDetailsRepository = AppDataSource.getRepository(StockTransferSlipDetails);
  //   try {
  //     const stsDetails = await stsDetailsRepository.findOne({ where: { id: Number(id) } });
  //     if (stsDetails) {
  //       stsDetailsRepository.merge(stsDetails, req.body);
  //       const updatedstsDetails = await stsDetailsRepository.save(stsDetails);
  //       res.status(200).json(updatedstsDetails);
  //     } else {
  //       res.status(404).json({ message: "STS Details not found" });
  //     }
  //   } catch (error) {
  //     res.status(400).json({ message: "Error updating STS Details" });
  //   }
  // }

  public async delete(req: Request, res: Response): Promise<void> {
    const { lotDetailsId, stsDetailsId } = req.params;
    const lotDetailsRepository = AppDataSource.getRepository(StockTransferSlipLotDetails);
    const stsDetailsRepository = AppDataSource.getRepository(StockTransferSlipDetails);

    try {
      // ตรวจว่าเหลือกี่ล็อตใน sts_detail นี้
      const count = await lotDetailsRepository.count({
        where: { sts_detail: { id: Number(stsDetailsId) } }
      });

      // ลบ lot_detail ที่ระบุ
      const result = await lotDetailsRepository.delete(Number(lotDetailsId));

      if (result.affected) {
        // ถ้าเป็นล็อตสุดท้าย → ลบ sts_detail ทิ้งด้วย
        if (count === 1) {
          await stsDetailsRepository.delete(Number(stsDetailsId));
        }
        res.status(204).send();
      } else {
        res.status(404).json({ message: "Lot Details not found" });
      }

      const allLotDetails = await lotDetailsRepository.find({
        where: { sts_detail: { id: Number(stsDetailsId) } },
      });

      // คำนวณผลรวมจากรายการทั้งหมด
      const totalOrdered = allLotDetails.reduce((sum, item) => sum + item.quantity_ordered, 0);
      const totalSent = allLotDetails.reduce((sum, item) => sum + item.quantity_sent, 0);

      // อัปเดตไปที่ sts_detail
      const stsDetail = await stsDetailsRepository.findOneOrFail({ where: { id: Number(stsDetailsId) } });
      stsDetail.total_quantity_ordered = totalOrdered;
      stsDetail.total_quantity_sent = totalSent;
      await stsDetailsRepository.save(stsDetail);
      res.status(204).send();

    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }


}
