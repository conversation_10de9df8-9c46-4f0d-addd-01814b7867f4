<template>
  <q-dialog v-model="isOpen" maximized>
    <q-card style="max-width: 1100px">
      <q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">รายละเอียดการสั่งซื้อสินค้า</div>
          <q-btn icon="close" @click="closeDialog" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section style="overflow: auto; max-height: 80vh">
        <!-- รายละเอียดบริษัท -->
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            บริษัทจำหน่ายสินค้า
          </div>
          <div class="shadow-2 container">
            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-1 q-mt-md" style="margin-left: 20px">บริษัท</div>
              <div class="col-12 col-md-10">
                <q-select class="input-container col-9" v-model="store.form.supplier"
                  :options="supplierStore.suppliers.filter((supplier) => supplier.type.id === 2)" option-label="name"
                  option-value="id" dense borderless map-options />
              </div>
            </div>

            <div class="row q-col-gutter-md" style="margin-top: 10px">
              <div class="col-12 col-md-1 q-mt-md" style="margin-left: 20px">ชื่อผู้ติดต่อ</div>
              <div class="col-12 col-md-4">
                <q-input class="input-container" v-model="store.form.contact" dense borderless type="text"
                  style="width: 100%" />
              </div>

              <div class="col-12 col-md-1 q-mt-md">ที่อยู่</div>
              <div class="col-12 col-md-5">
                <q-input class="input-container" v-model="store.form.address" dense borderless type="textarea"
                  style="width: 100%" />
              </div>
            </div>
          </div>
        </div>
        <!-- การสั่งซื้อ -->
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            รายการสั่งซื้อสินค้า
          </div>
          <div class="shadow-2 container">
            <div class="row justify-between">
              <div class="col-11 row q-my-sm">
                <div class="col-1 q-mt-md q-pr-md">เลขที่</div>
                <q-input dense borderless class="input-container" v-model="store.form.id"
                  style="width: 224px; height: 40px" readonly />
                <div class="col-1 q-pr-md flex flex-center" style="margin-left: 45px">สถานะ</div>
                <div class="row q-col-gutter-sm">
                  <q-radio keep-color v-model="store.form.status" val="เตรียมรายการ" label="เตรียมรายการ" color="orange"
                    style="color: orange" size="sm" />
                  <q-radio keep-color v-model="store.form.status" val="ดำเนินการ" label="ดำเนินการ" color="blue"
                    style="color: royalblue" size="sm" />
                  <q-radio keep-color v-model="store.form.status" val="เสร็จสมบูรณ์" label="เสร็จสมบูรณ์" color="green"
                    style="color: green" size="sm" />
                  <q-radio keep-color v-model="store.form.status" val="ยกเลิก" label="ยกเลิก" color="red"
                    style="color: red" size="sm" />
                </div>
              </div>
              <div class="col-3 width-column">
                <div class="row items-center q-mb-sm" style="margin-top: 10px" justify-content: space-between>
                  <div class="col-3 q-mt-sm q-pr-md">วันที่</div>
                  <q-input dense borderless class="input-container col-9" v-model="formattedDate" readonly />

                  <div class="col-12 row q-col-gutter-ms" style="margin-top: 10px">
                    <div class="col-3 q-mt-sm">พนักงาน</div>

                    <q-select class="input-container col-9" v-model="store.form.user" :options="userStore.users"
                      option-label="name" option-value="id" dense borderless map-options />

                    <!-- <q-select v-model="store.form.supplier.id" :options="updateSupplier" option-value="value"
                      option-label="label" emit-value map-options dense borderless class="input-container" /> -->
                  </div>

                  <div class="col-lg-12 row" style="margin-top: 10px">
                    <div class="col-3 q-mt-sm q-pr-md">รวมเงิน</div>
                    <q-input dense borderless class="input-container col-9" v-model="store.form.po_total"
                      :rules="[(val) => /^\d*\.?\d*$/.test(val)]" type="number" style="height: 40px" />
                    <div class="col-12 row" style="margin-top: 10px">
                      <div class="col-3 q-mt-sm q-pr-md">ภาษี (%)</div>
                      <q-input number dense borderless class="input-container col-3" v-model="store.form.tax"
                        type="number" />

                      <div class="col-3 q-mt-sm q-pr-md text-center">เงินภาษี</div>
                      <q-input dense borderless class="input-container-2 col-3" v-model="store.form.tax_total"
                        type="number" />
                      <!-- <div class="col-3 q-mt-sm q-pr-md">รวมเงินสุทธิ</div>
                      <q-input dense borderless class="input-container col-9" v-model="store.form.po_total"
                        :rules="[(val) => /^\d*$/.test(val)]" style="height: 40px" /> -->
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-3 width-column">
                <div class="col-12 row" style="margin-top: 10px">
                  <div class="col-4 q-mt-sm q-pr-md">วันที่สั่งสินค้า</div>
                  <div class="col-12 col-md-8">
                    <q-input dense borderless class="input-container" v-model="formattedOrderDate" style="width: 100%">
                      <q-icon name="event" size="md" color="black" style="cursor: pointer; margin-top: 5px">
                        <q-popup-proxy transition-show="scale" transition-hide="scale">
                          <q-date v-model="formattedOrderDate" mask="DD/MM/YYYY" color="teal" />
                        </q-popup-proxy>
                      </q-icon>
                    </q-input>
                  </div>
                </div>
                <div class="col-12 row" style="margin-top: 10px">
                  <div class="col-4 q-mt-md q-pr-md">หมายเหตุ</div>
                  <div class="col-12 col-md-8">
                    <q-input dense borderless class="input-container" v-model="store.form.note"
                      style="width: 100%; height: 90px" type="text" />
                  </div>
                </div>
                <div class="col-12 row" style="margin-top: 10px">
                  <div class="col-4 q-mt-sm q-pr-md">รวมจำนวน</div>
                  <div class="col-4 col-md-4">
                    <q-input dense borderless class="input-container" v-model="store.form.order_total" label="สั่ง"
                      style="width: 100%" type="number" />
                  </div>
                  <div class="col-4 col-md-4">
                    <q-input dense borderless class="input-container" v-model="store.form.receive_total" label="รับ"
                      style="margin-left: 2px; width: 100%" type="number" />
                  </div>
                </div>
              </div>
              <div class="col-3 width-column">
                <div class="col-12 row">
                  <div class="col-xl-1" style="font-size: 12px">ส่วนลดท้ายบิล</div>
                  <div class="col-3 mini-container width-column">
                    <div class="col-2">
                      <div class="row items-center">
                        <div style="font-size: 13px; margin-left: 10px">จำนวนส่วนลด</div>
                        <q-input dense borderless class="input-container-v3" v-model="store.form.order_discount"
                          style="margin-left: 10px" type="number" />
                      </div>
                    </div>
                  </div>
                  <div class="col-12 row" style="margin-bottom: 20px">
                    <div class="col-4 q-mt-sm q-pr-md" style="font-size: 12px; margin-top: 5px">
                      สินค้ามีภาษี
                    </div>
                    <div class="col-3 mini-container width-column">
                      <div class="col-4" style="font-size: 13px">
                        <div class="col-xl-2 q-mt-md">ราคาสินค้า</div>
                        <div class="col-12 col-md-8 text-center">
                          <q-radio v-model="store.form.product_price_tax" val="รวมภาษี" label="รวมภาษี"
                            color="grey-7" />
                          <q-radio v-model="store.form.product_price_tax" val="ไม่รวมภาษี" label="ไม่รวมภาษี"
                            color="grey-7" />
                        </div>
                        <div class="col-4 q-mt-sm q-pr-md">ส่วนลดท้ายบิล</div>
                        <div class="col-12 col-md-8 text-center" style="margin-bottom: 10px">
                          <q-radio v-model="store.form.order_discount_tax" val="ก่อนภาษี" label="ก่อนภาษี"
                            color="grey-7" />
                          <q-radio v-model="store.form.order_discount_tax" val="หลังภาษี" label="หลังภาษี"
                            color="grey-7" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </q-card-section>
      <q-card-actions align="center">
        <q-btn class="btn-accept" dense flat label="บันทึก" @click="saveDialog" />
        <q-btn class="btn-cancel" dense flat label="ยกเลิก" @click="closeDialog" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { usePurchaseOrderStore } from 'src/stores/purchaseorder'
import { useSupplierStore } from 'src/stores/supplier'
import { computed, watch } from 'vue'
import { date } from 'quasar' // ใช้ quasar date utility หากต้องการจัดการวันที่
import { onMounted } from 'vue'
import { useUserStore } from 'src/stores/userStore'
import type { PurchaseOrder } from 'src/types/purchaseOrder'
import { useAuthStore } from 'src/stores/authStore'

// Props and emits for v-model support
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'po-saved': []
  'open-po-details': [poData: PurchaseOrder, mode: string]
}>()

// Computed property for v-model
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const store = usePurchaseOrderStore()
const userStore = useUserStore()
const authStore = useAuthStore()
const supplierStore = useSupplierStore()

const closeDialog = () => {
  isOpen.value = false
  store.resetForm()
}

const saveDialog = async () => {
  store.currentStatus = 'เตรียมรายการ'
  store.form.receive_status = 'รอรับ'
  console.log(store.form)
  await store.addOrder(store.form)

  // Emit the po-saved event
  emit('po-saved')

  // Close this dialog
  isOpen.value = false

  // Emit event to open PODetails dialog with the saved PO data
  emit('open-po-details', store.form, 'add')

  await store.fetchOrders()
}
watch(
  () => props.modelValue,
  () => {
    store.form.order_discount_tax = 'ก่อนภาษี'
    store.form.product_price_tax = 'ไม่รวมภาษี'
    store.form.tax = 7.00
    store.form.po_total = 0.00
    const currentUser = authStore.currentUser || userStore.currentUser
    if (currentUser) {
      store.form.user = {
        id: currentUser.id,
        password: currentUser.password || '',
        name: currentUser.name,
        image: currentUser.image || '',
        tel: currentUser.tel || '',
        role: currentUser.role || '',
        hour_work: currentUser.hour_work || 0,
        sick_level: currentUser.sick_level || 0,
        personal_leave: currentUser.personal_leave || 0,
        branch: currentUser.branch || {
          id: 0,
          name: '',
          address: '',
        },
      }

      // Set branch from user
      store.form.branch = currentUser.branch || {
        id: 0,
        name: '',
        address: '',
      }
    } else {
      // Fallback if no current user
      store.form.user = {
        id: 0,
        password: '',
        name: '',
        image: '',
        tel: '',
        role: '',
        hour_work: 0,
        sick_level: 0,
        personal_leave: 0,
        branch: {
          id: 0,
          name: '',
          address: '',
          contact_name: '',
          contact_phone: '',
        },
      }
    }
  },
  { deep: true }
);
watch(
  () => store.form.supplier,
  (newSupplier) => {
    if (newSupplier) {
      store.form.contact = newSupplier.contact_name || '';
      store.form.address = newSupplier.address || ''
    }
  },
  { deep: true }
);


onMounted(async () => {
  if (!store.form.date) {
    store.form.date = new Date(); // กำหนดค่าเริ่มต้นถ้ายังไม่มี
  }
  await supplierStore.loadSuppliers()
  await userStore.fetchUsers()
  store.form.order_date = new Date()
  store.currentStatus = 'เตรียมรายการ'
})

const formattedOrderDate = computed({
  get() {
    // ตรวจสอบว่า store.form.order_date มีค่าไหม
    const dateValue = store.form.order_date ? store.form.order_date : new Date()
    return date.formatDate(dateValue, 'DD/MM/YYYY') // แปลงจาก Date เป็น string ในรูปแบบ dd/mm/yyyy
  },
  set(value: string) {
    const parts = value.split('/') // แยกค่าโดยใช้ '/'

    if (parts.length === 3) {
      const [day, month, year] = parts
      const newDate = new Date(`${year}-${month}-${day}`) // แปลงกลับเป็น Date
      store.form.order_date = newDate // อัพเดทค่าใน store
    } else {
      store.form.order_date = new Date() // หากรูปแบบไม่ถูกต้อง, ใช้วันที่ปัจจุบัน
    }
  },
})

const formattedDate = computed({
  get() {
    return date.formatDate(store.form.date, 'DD/MM/YYYY') // แปลงจาก Date เป็น string ในรูปแบบ YYYY-MM-DD
  },
  set(value: string) {
    const newDate = new Date(value) // แปลงจาก string กลับเป็น Date
    store.form.date = newDate // อัพเดทค่าใน store
  },
})
// const formattedDate = (() => {
//   let dateValue = store.form.date;
//   dateValue = new Date()

//   if (!dateValue) {
//     console.error("❌ order_date is undefined or null");
//     return "00/00/0000"; // ค่าเริ่มต้นป้องกัน error
//   }

//   // ตรวจสอบและแปลงค่า dateValue เป็น Date object ถ้ายังเป็น string
//   const parsedDate = typeof dateValue === "string" ? new Date(dateValue) : dateValue;

//   if (!(parsedDate instanceof Date) || isNaN(parsedDate.getTime())) {
//     // console.error(`❌ Invalid date format: ${dateValue}`);
//     return "00/00/0000"; // หรือใช้ค่าปัจจุบันแทน
//   }

//   return parsedDate.toISOString().slice(0, 10).replace(/-/g, "");
// })();
// const isFormValid = computed(() => {
//   return (
//     store.form.supplier.name &&
//     store.form.contact &&
//     store.form.address &&
//     store.form.id &&
//     store.form.status &&
//     store.form.date &&
//     store.form.user.name &&
//     store.form.po_total &&
//     store.form.tax &&
//     store.form.tax_total &&
//     store.form.order_date &&
//     store.form.note &&
//     store.form.order_total &&
//     store.form.receive_total &&
//     store.form.order_discount &&
//     store.form.product_price_tax &&
//     store.form.order_discount_tax
//   )
// })
</script>

<style scoped>
.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.mini-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container-2 {
  padding-left: 5px;
  padding-right: 5px;
  background-color: white;
  border-radius: 5px;
}

.btn-accept {
  background-color: #36b54d;
  margin-right: 10px;
}

.btn-cancel {
  background-color: #9c2c2c;
  margin-right: 10px;
}

.gap-container {
  margin-bottom: 20px;
}

.dialog-container {
  border-radius: 10px;
}

.input-container-v2 {
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  background-color: #83a7d8;
}

.input-container-v3 {
  padding-left: 10px;
  padding-right: 10px;
  margin-top: 5px;
  margin-bottom: 5px;
  border-radius: 5px;
  background-color: #d6e4f6;
  width: 150px;
}

.width-column {
  width: 300px;
}

.width-column-2 {
  width: 390px;
}

.width-column-3 {
  width: 350px;
}

.column-table {
  height: 40px;
  background-color: #294888;
  color: white;
}

.column-table-2 {
  height: 40px;
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border: 2px solid #294888;
}
</style>
