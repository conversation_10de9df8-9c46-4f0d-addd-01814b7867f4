import { Request, Response } from "express";
import { AppDataSource } from "../../Config/db";
import { GoodsReceipt } from "../../Models/GoodsReceipt";
import { GoodsReceiptDetails } from "../../Models/GoodsReceiptDetails";

export class GoodsReceiptDashboardController {
  // GET /api/goods-receipt/volume-by-month
  public async getVolumeByMonth(req: Request, res: Response): Promise<void> {
    try {
      const grRepository = AppDataSource.getRepository(GoodsReceipt);

      const result = await grRepository
        .createQueryBuilder("gr")
        .leftJoin("gr.gr_details", "details")
        .select([
          "strftime('%Y-%m', gr.receive_date) as month",
          "SUM(details.total_receive_quantity) as total_quantity",
          "COUNT(DISTINCT gr.id) as receipt_count",
          "SUM(details.total_receive_quantity * details.receive_price_before_tax) as total_value"
        ])
        .groupBy("strftime('%Y-%m', gr.receive_date)")
        .orderBy("month", "ASC")
        .getRawMany();

      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  // GET /api/goods-receipt/top-suppliers
  public async getTopSuppliers(req: Request, res: Response): Promise<void> {
    try {
      const grRepository = AppDataSource.getRepository(GoodsReceipt);

      const result = await grRepository
        .createQueryBuilder("gr")
        .leftJoin("gr.distributor", "supplier")
        .leftJoin("gr.gr_details", "details")
        .select([
          "supplier.id as supplier_id",
          "supplier.name as supplier_name",
          "COUNT(DISTINCT gr.id) as receipt_count",
          "SUM(details.receive_quantity) as total_quantity",
          "SUM(details.total_receive_quantity * details.cost_unit) as total_value"
        ])
        .groupBy("supplier.id")
        .addGroupBy("supplier.name")
        .orderBy("receipt_count", "DESC")
        .getRawMany();

      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  // GET /api/goods-receipt/value-by-month
  public async getValueByMonth(req: Request, res: Response): Promise<void> {
  try {
    const grDetailsRepository = AppDataSource.getRepository(GoodsReceiptDetails);

    const result = await grDetailsRepository
      .createQueryBuilder("details")
      .leftJoin("details.gr", "gr")
      .select([
        "strftime('%Y-%m', gr.receive_date) as month",
        "SUM(details.total_receive_quantity * details.receive_price_before_tax) as total_value",
        "AVG(details.receive_price_before_tax) as avg_price",
        "COUNT(details.id) as item_count"
      ])
      .groupBy("strftime('%Y-%m', gr.receive_date)")
      .orderBy("month", "ASC")
      .getRawMany();

    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
}

}
