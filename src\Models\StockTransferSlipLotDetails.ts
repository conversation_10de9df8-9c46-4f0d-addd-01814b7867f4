import { Column, <PERSON>tity, ManyToOne, PrimaryGeneratedColumn } from "typeorm";
import { StockTransferSlipDetails } from "./StockTransferSlipDetails";
import { Stock } from "./Stock";
import { LotStatus } from "../enums/lot-status";

@Entity()
export class StockTransferSlipLotDetails {
  @PrimaryGeneratedColumn()
  id!: number;

  @ManyToOne(() => StockTransferSlipDetails, detail => detail.lot_details)
  sts_detail!: StockTransferSlipDetails;

  @ManyToOne(() => Stock)
  stock!: Stock;

  @Column({
    type: "text",
    default: LotStatus.PREPARING,
    nullable: true
  })
  lot_status!: LotStatus;

  @Column({ type: "numeric", default: 0 })
  quantity_ordered!: number;

  @Column({ type: "numeric", default: 0 })
  quantity_sent!: number;
}