<template>
  <q-dialog v-model="isOpen" maximized>
    <q-card style="max-width: 1100px"><q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">รายละเอียดการรับสินค้า</div>
          <q-btn icon="close" @click="closeDialog()" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section style="overflow: auto; max-height: 80vh">
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            รายละเอียดบริษัทจำหน่ายสินค้า
            <q-btn class="btn-add" label="ค้นหาบริษัทจำหน่ายสินค้า" style="margin-right: 20px"
              @click="openSearchDistributorDialog()"></q-btn>
            หรือ
            <q-btn class="btn-add" label="ค้นหารายการสั่งซื้อสินค้า" @click="openSearchGRDialog()"></q-btn>
          </div>
          <div class="shadow-2 container">
            <div class="row q-col-gutter-md" style="margin-left: 20px">
              <div class="col-12 col-md-1 q-mt-md" style="margin-left: 20px">รหัสบริษัท</div>
              <div class="col-12 col-md-4">
                <q-input class="input-container" dense borderless v-model="store.form.distributor.supplier_number"
                  type="text" />
              </div>

              <div class="col-12 col-md-1 q-mt-md" style="margin-left: 20px">ชื่อผู้ติดต่อ</div>
              <div class="col-12 col-md-4">
                <q-input class="input-container" v-model="store.form.distributor.contact_name" dense borderless
                  type="text" style="width: 125%" />
              </div>
            </div>

            <div class="row q-col-gutter-md" style="margin-top: 10px; margin-left: 20px">
              <div class="col-12 col-md-1 q-mt-md" style="margin-left: 20px">ชื่อบริษัท</div>
              <div class="col-12 col-md-4">
                <q-input class="input-container" v-model="store.form.distributor.name" dense borderless type="text"
                  style="width: 100%" />
              </div>

              <div class="col-12 col-md-1 q-mt-md" style="margin-left: 20px">ที่อยู่</div>
              <div class="col-12 col-md-5">
                <q-input class="input-container" v-model="store.form.distributor.address" dense borderless
                  type="textarea" />
              </div>
            </div>
          </div>
        </div>

        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            รายละเอียดการรับสินค้า
            <q-btn class="btn-add" label="แก้ไข" style="color: red"></q-btn>
          </div>
          <div class="shadow-2 container">
            <div class="row justify-between">
              <div class="col-11 row q-my-sm">
                <div class="col-1 q-mt-md q-pr-md" style="margin-left: 10px">เลขที่</div>
                <q-input dense borderless class="input-container" v-model="store.form.id"
                  style="width: 175px; height: 40px; margin-left: 22px" readonly />
                <div class="col-2 q-pr-md flex flex-center" style="margin-left: 30px">สถานะ</div>
                <div class="row q-col-gutter-sm" style="margin-left: 5px">
                  <q-radio keep-color v-model="store.form.status" val="เตรียมรายการ" label="เตรียมรายการ" color="orange"
                    style="color: orange" size="sm" />
                  <q-radio keep-color v-model="store.form.status" val="ดำเนินการ" label="ดำเนินการ" color="blue"
                    style="color: royalblue" size="sm" />
                  <q-radio keep-color v-model="store.form.status" val="เสร็จสมบูรณ์" label="เสร็จสมบูรณ์" color="green"
                    style="color: green" size="sm" />
                  <q-radio keep-color v-model="store.form.status" val="ยกเลิก" label="ยกเลิก" color="red"
                    style="color: red" size="sm" />
                </div>
              </div>
              <div class="col-3 width-column" style="margin-left: 10px">
                <div class="row items-center q-mb-sm" style="margin-top: 10px" justify-content: space-between>
                  <div class="col-4 q-mt-sm q-pr-md">วันที่รับ</div>
                  <q-input dense borderless class="input-container col-7" v-model="formattedDate" readonly />

                  <div class="col-12 row q-col-gutter-ms" style="margin-top: 20px">
                    <div class="col-4 q-mt-sm q-pr-md">รวมเงิน</div>
                    <q-input dense borderless class="input-container col-7" :model-value="grTotalFormatted"
                      @@update:model-value="onInput" :rules="[(val) => /^\d*$/.test(val)]" style="height: 40px" />
                  </div>
                  <div class="col-12 row" style="margin-top: 20px">
                    <div class="col-4 q-mt-sm q-pr-md">มูลค่าภาษี</div>
                    <q-input number dense borderless class="input-container col-7" v-model="store.form.tax"
                      type="number" />
                  </div>
                  <div class="col-12 row" style="margin-top: 20px">
                    <div class="col-4 q-mt-sm q-pr-md">รวมเงินสุทธิ</div>
                    <q-input number dense borderless class="input-container col-7" v-model="store.form.tax_total"
                      type="number" />
                  </div>
                </div>
              </div>
              <div class="col-3 width-column">
                <div class="col-12 row q-col-gutter-ms" style="margin-top: 20px">
                  <div class="col-5 q-mt-sm">พนักงาน</div>
                  <q-input class="input-container col-7" v-model="store.form.user.name" dense borderless readonly />
                </div>
                <div class="col-12 row" style="margin-top: 20px">
                  <div class="col-5 q-mt-sm q-pr-md">เลขที่ใบสั่งซื้อ</div>
                  <q-input number dense borderless class="input-container col-7" v-model="store.form.po_code" />
                </div>
                <div class="col-12 row" style="margin-top: 20px">
                  <div class="col-5 q-mt-sm q-pr-md">เลขที่ใบกำกับภาษี</div>
                  <q-input number dense borderless class="input-container col-7"
                    v-model="store.form.tax_invoice_number" />
                </div>
                <div class="col-12 row" style="margin-top: 20px">
                  <div class="col-5 q-mt-sm q-pr-md">วันที่ใบกำกับภาษี</div>

                  <q-input dense borderless class="input-container" v-model="formattedInvoiceDate" style="width: 58%">
                    <q-icon name="event" size="md" color="black" style="cursor: pointer; margin-top: 5px">
                      <q-popup-proxy transition-show="scale" transition-hide="scale">
                        <q-date v-model="formattedInvoiceDate" mask="DD/MM/YYYY" color="teal" />
                      </q-popup-proxy>
                    </q-icon>
                  </q-input>
                </div>
                <div class="col-12 row" style="margin-top: 20px">
                  <div class="col-5 q-mt-sm q-pr-md">วันที่ให้เครดิต</div>

                  <q-input dense borderless class="input-container" v-model="formattedCreditDate" style="width: 58%">
                    <q-icon name="event" size="md" color="black" style="cursor: pointer; margin-top: 5px">
                      <q-popup-proxy transition-show="scale" transition-hide="scale">
                        <q-date v-model="formattedCreditDate" mask="DD/MM/YYYY" color="teal" />
                      </q-popup-proxy>
                    </q-icon>
                  </q-input>
                </div>
                <div class="col-12 row" style="margin-top: 20px">
                  <div class="col-5 q-mt-sm q-pr-md">จำนวนวันเครดิต</div>
                  <q-input number dense borderless class="input-container col-7" v-model="store.form.credit_days" />
                </div>
              </div>
              <div class="col-3 width-column">
                <div class="col-12 row justify-center" style="margin-top: 20px">
                  <q-checkbox class="mini-container-header" v-model="store.form.is_tax_invoice"
                    label="ใบรับสินค้ามีภาษี" color="blue-grey-9" size="xs" />
                  <q-slide-transition>
                    <div v-if="store.form.is_tax_invoice" class="mini-container" style="font-size: 13px">
                      <!-- รายละเอียดภาษี -->
                      <div class="row q-mb-sm">
                        <div class="col-6" style="margin-top: 15px; margin-left: 5px">
                          ภาษีมูลค่าเพิ่ม (%)
                        </div>
                        <q-input v-model="store.form.vat_percent" dense borderless class="input-container-v3"
                          style="width: 100px" />
                      </div>
                      <div class="col-6" style="margin-left: 5px">ราคารายการสินค้า</div>
                      <q-option-group v-model="store.form.product_price_tax" :options="[
                        { label: 'รวมภาษี', value: 'รวมภาษี' },
                        { label: 'ไม่รวมภาษี', value: 'ไม่รวมภาษี' },
                      ]" type="radio" color="grey-8" inline size="xs" />
                    </div>
                  </q-slide-transition>
                </div>
                <div class="col-12 row justify-center" style="margin-top: 20px">
                  <q-checkbox class="mini-container-header" v-model="store.form.is_manual_discount_before_tax"
                    label="ส่วนลดท้ายบิล ก่อนภาษี" color="blue-grey-9" size="xs" />
                  <q-slide-transition>
                    <div v-if="store.form.is_manual_discount_before_tax" class="mini-container">
                      <!-- รายละเอียดภาษี -->
                      <div class="row q-mb-sm items-center" style="font-size: 13px; margin-top: 10px">
                        <div class="col-auto" style="margin-left: 10px">ส่วนลด</div>
                        <div class="col-auto">
                          <q-option-group v-model="store.form.order_discount_tax" :options="[
                            { label: 'รวมภาษี', value: 'รวมภาษี' },
                            { label: 'ไม่รวมภาษี', value: 'ไม่รวมภาษี' },
                          ]" type="radio" color="grey-8" size="xs" inline />
                        </div>
                        <div class="col-auto row items-center" style="margin-left: 10px">
                          <q-radio v-model="store.form.is_before_tax_discount" val="% ส่วนลด" label="% ส่วนลด"
                            color="grey-7" size="xs" class="q-mr-sm" />
                          <q-input v-model="store.form.before_tax_discount_percent" dense borderless
                            class="input-container-v3 q-mr-md" style="width: 100px" />
                        </div>

                        <div class="col-auto row items-center" style="margin-left: 10px">
                          <q-radio v-model="store.form.is_before_tax_discount" val="ส่วนลด" label="ส่วนลด"
                            color="grey-7" size="xs" class="q-mr-sm" />
                          <q-input v-model="store.form.before_tax_discount_amount" dense borderless
                            class="input-container-v3 q-mr-md" style="width: 100px; margin-left: 20px" />
                        </div>
                      </div>
                    </div>
                  </q-slide-transition>
                </div>
                <div class="col-12 row justify-center" style="margin-top: 20px">
                  <q-checkbox class="mini-container-header" v-model="store.form.is_manual_discount_after_tax"
                    label="ส่วนลดท้ายบิล หลังภาษี" color="blue-grey-9" size="xs" />
                  <q-slide-transition>
                    <div v-if="store.form.is_manual_discount_after_tax" class="mini-container">
                      <!-- รายละเอียดภาษี -->
                      <div class="row q-mb-sm" style="font-size: 13px; margin-top: 10px">
                        <q-radio v-model="store.form.is_after_tax_discount" val="% ส่วนลด" label="% ส่วนลด"
                          color="grey-7" size="xs" style="margin-right: 10px; margin-left: 10px" />

                        <q-input v-model="store.form.after_tax_discount_percent" dense borderless
                          class="input-container-v3" />
                        <q-radio v-model="store.form.is_after_tax_discount" val="ส่วนลด" label="ส่วนลด" color="grey-7"
                          size="xs" style="margin-right: 25px; margin-left: 10px" />

                        <q-input v-model="store.form.after_tax_discount_amount" dense borderless
                          class="input-container-v3" />
                      </div>
                    </div>
                  </q-slide-transition>
                </div>

                <div class="col-12 row justify-center" style="margin-top: 10px">
                  <div class="q-mt-sm q-pr-md text-center mini-container-header" style="padding: 5px">
                    การคำนวณราคาทุน
                  </div>
                  <div class="col-12 col-md-8 mini-container">
                    <q-radio v-model="store.form.is_discount_applied" val="ใช้ส่วนลดในการคำนวณทุนสินค้า"
                      label="ใช้ส่วนลดในการคำนวณทุนสินค้า" color="grey-7" size="xs" />
                    <q-radio v-model="store.form.is_tax_included" val="เพิ่มภาษีลงในราคาทุนสินค้า"
                      label="เพิ่มภาษีลงในราคาทุนสินค้า" color="grey-7" size="xs" hidden />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </q-card-section>
      <q-card-actions align="center">
        <q-btn class="btn-accept" dense flat label="บันทึก" @click="saveDialog" />
        <q-btn class="btn-cancel" dense flat label="ยกเลิก" @click="closeDialog" />
      </q-card-actions>
    </q-card>

  </q-dialog>

  <!-- Search Dialogs -->
  <SearchByDistributor v-model="searchDistributorDialogOpen" @distributor-selected="onDistributorSelected" />
  <SearchGR v-model="searchGRDialogOpen" @gr-selected="onGRSelected" />
  <GRDetailsDialog v-model="grDetailsDialogOpen" :mode="modeGrDetails"></GRDetailsDialog>
  <GRDetailsforDistribu v-model="grDetailsforDistribuDialogOpen" :mode="modeGrDetailsforDistribu">
  </GRDetailsforDistribu>
</template>

<script setup lang="ts">
import { date } from 'quasar'
import { useGoodsReceiptStore } from 'src/stores/goodsreceipt'
import { useAuthStore } from 'src/stores/authStore'
import { useUserStore } from 'src/stores/userStore'
import { computed, watch, ref } from 'vue'
import SearchGR from './SearchGR.vue'
import SearchByDistributor from './SearchByDistributor.vue'
import GRDetailsDialog from './GRDetailsDialog.vue'
import GRDetailsforDistribu from './GRDetailsforDistribu.vue'
import type { Supplier } from 'src/types/supplier'
import type { PurchaseOrder } from 'src/types/purchaseOrder'


const store = useGoodsReceiptStore()
const authStore = useAuthStore()
const userStore = useUserStore()
const grDetailsDialogOpen = ref(false)
const modeGrDetails = ref('add')
const grDetailsforDistribuDialogOpen = ref(false)
const modeGrDetailsforDistribu = ref('add')
// Dialog state for search dialogs
const searchDistributorDialogOpen = ref(false)
const searchGRDialogOpen = ref(false)
interface Props {
  modelValue: boolean
  mode?: string
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'add'
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'item-updated': []
}>()

const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})
const closeDialog = () => {
  isOpen.value = false
  store.resetForm()
}

const saveDialog = async () => {

  // Check if form has PO (Purchase Order)
  const hasPO = store.po &&
    typeof store.po.id === 'number' &&
    !isNaN(store.po.id) &&
    store.po.id > 0 &&
    store.po.code &&
    store.po.code.trim() !== ''
  isOpen.value = false
  if (hasPO) {
    await store.createFromPO(store.po.id)

    // Form has PO - open GRDetailsDialog
    await store.fetchGoodsReceiptByStatus()
    await store.fetchGoodsReceiptById(store.form.id)
    grDetailsDialogOpen.value = true
    modeGrDetails.value = 'add'
  } else {
    // Validate required fields for distributor-based GR
    await store.createFromDistributor(store.form)
    // Form doesn't have PO - open GRDetailsforDistribu
    await store.fetchGoodsReceiptByStatus()
    grDetailsforDistribuDialogOpen.value = true
    modeGrDetailsforDistribu.value = 'add'
  }
}
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      // Initialize form with default values
      store.form.distributor = {
        id: 0,
        supplier_number: '',
        name: '',
        address: '',
        tel: '',
        tax_number: '',
        fax: '',
        contact_name: '',
        email: '',
        type: {
          id: 0,
          name: '',
        },
        isactive: false,
        credit_days: 0,
      }

      // Set current user information
      const currentUser = authStore.currentUser || userStore.currentUser
      if (currentUser) {
        store.form.user = {
          id: currentUser.id,
          password: currentUser.password || '',
          name: currentUser.name,
          image: currentUser.image || '',
          tel: currentUser.tel || '',
          role: currentUser.role || '',
          hour_work: currentUser.hour_work || 0,
          sick_level: currentUser.sick_level || 0,
          personal_leave: currentUser.personal_leave || 0,
          branch: currentUser.branch || {
            id: 0,
            name: '',
            address: '',
          },
        }

        // Set branch from user
        store.form.branch = currentUser.branch || {
          id: 0,
          name: '',
          address: '',
        }
      } else {
        // Fallback if no current user
        store.form.user = {
          id: 0,
          password: '',
          name: '',
          image: '',
          tel: '',
          role: '',
          hour_work: 0,
          sick_level: 0,
          personal_leave: 0,
          branch: {
            id: 0,
            name: '',
            address: '',
            contact_name: '',
            contact_phone: '',
          },
        }
      }
    }
  }
)
// Dialog functions
const openSearchDistributorDialog = () => {
  searchDistributorDialogOpen.value = true
}

const openSearchGRDialog = () => {
  searchGRDialogOpen.value = true
}

const onDistributorSelected = (distributor: Supplier) => {
  // Handle distributor selection
  store.form.distributor = distributor
  // Clear PO data when distributor is selected directly
  store.po = {} as PurchaseOrder
  console.log('Distributor selected:', distributor)
}

const onGRSelected = (poData: PurchaseOrder) => {
  // Handle GR selection
  console.log('GR selected:', poData)
  store.po = poData
  store.form.branch = poData.branch //branch from po only !!!
  // Update distributor from PO when PO is selected
  if (poData.supplier) {
    store.form.distributor = poData.supplier
  }
}

const formattedDate = computed({
  get() {
    return date.formatDate(store.form.date_document, 'DD/MM/YYYY') // แปลงจาก Date เป็น string ในรูปแบบ YYYY-MM-DD
  },
  set(value: string) {
    const newDate = new Date(value) // แปลงจาก string กลับเป็น Date
    store.form.date_document = newDate // อัพเดทค่าใน store
  },
})
const formattedInvoiceDate = computed({
  get() {
    if (!store.form || !store.form.tax_invoice_date) {
      return '';
    }
    return date.formatDate(store.form.tax_invoice_date, 'DD/MM/YYYY');
  },
  set(value: string) {
    if (!store.form) return;
    const parts = value.split('/')
    if (parts.length === 3) {
      const [day, month, year] = parts
      if (day && month && year) {
        const newDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day))
        store.form.tax_invoice_date = newDate;
      }
    } else {
      store.form.tax_invoice_date = new Date()
    }
  },
})
const formattedCreditDate = computed({
  get() {
    if (!store.form || !store.form.credit_date) {
      return '';
    }
    return date.formatDate(store.form.credit_date, 'DD/MM/YYYY');
  },
  set(value: string) {
    if (!store.form) return;
    const parts = value.split('/')
    if (parts.length === 3) {
      const [day, month, year] = parts
      if (day && month && year) {
        const newDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day))
        store.form.credit_date = newDate;
      }
    } else {
      store.form.credit_date = new Date()
    }
  },
})
const grTotalFormatted = computed({
  get() {
    return (store.form.gr_total ?? 0).toFixed(2);
  },
  set(val) {
    // แปลง input string เป็น number และเก็บใน store
    const num = parseFloat(val);
    store.form.gr_total = isNaN(num) ? 0 : num;
  }
});
function onInput(val: string) {
  grTotalFormatted.value = val; // เพื่อ trigger set
}
</script>
<style scoped>
.gap-container {
  margin-bottom: 20px;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.btn-add {
  background-color: #ffffff;
  color: #000000;
  border-radius: 5px;
  margin-left: 20px;
  font-size: 13px;
}

.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.mini-container-header {
  background-color: #83a7d8;
  border-radius: 3px 3px 0 0;
  width: 250px;
  height: 30px;
}

.mini-container {
  background-color: white;
  border-radius: 0 0 3px 3px;
  width: 250px;
}

.width-column {
  width: 300px;
}

.input-container-v3 {
  padding-left: 10px;
  padding-right: 10px;
  margin-top: 5px;
  margin-bottom: 5px;
  margin-left: 5px;
  border-radius: 5px;
  background-color: #d6e4f6;
  width: 120px;
}

.btn-accept {
  background-color: #36b54d;
  margin-right: 10px;
}

.btn-cancel {
  background-color: #9c2c2c;
  margin-right: 10px;
}
</style>
