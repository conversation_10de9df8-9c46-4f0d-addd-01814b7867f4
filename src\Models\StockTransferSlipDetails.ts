import { Column, <PERSON><PERSON><PERSON>, <PERSON>in<PERSON><PERSON><PERSON>n, ManyToOne, OneToMany, OneToOne, PrimaryGeneratedColumn } from "typeorm";
import { Product } from "./Product";
import { StockTransferSlip } from "./StockTransferSlip";
import { StockTransferOrderDetails } from "./StockTransferOrderDetails";
import { BranchReceive } from "./BranchReceive";
import { StockTransferSlipLotDetails } from "./StockTransferSlipLotDetails";
// import { Transform } from "class-transformer";

@Entity()
export class StockTransferSlipDetails {
  @PrimaryGeneratedColumn()
  id!: number;

  @ManyToOne(() => StockTransferSlip) // แก้จาก OneToOne เป็น ManyToOne
  @JoinColumn()
  // @Transform(({ value }) => value?.id)
  sts!: StockTransferSlip;


  // @OneToOne(() => StockTransferOrderDetails)
  // @JoinColumn()
  // sto_details!: StockTransferOrderDetails;

  @ManyToOne(() => StockTransferOrderDetails)
  @JoinColumn()
  sto_details!: StockTransferOrderDetails;

  @ManyToOne(() => Product, { onDelete: "CASCADE" })
  @JoinColumn()
  product!: Product;

  @Column({ type: "text", nullable: true })
  lot_number!: string;

  @Column({ type: "numeric", default: 0 }) // จำนวนที่สั่ง
  total_quantity_ordered!: number;

  @Column({ type: "numeric", default: 0 }) // จำนวนที่ส่ง
  total_quantity_sent!: number;

  @Column({ type: "text", nullable: true })
  sts_details_status!: string;

  @OneToMany(() => StockTransferSlipLotDetails, lot => lot.sts_detail, { cascade: true })
  lot_details!: StockTransferSlipLotDetails[];

  @OneToMany(() => BranchReceive, (br) => br.br_details)
  br!: BranchReceive[];
}