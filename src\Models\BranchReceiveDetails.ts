import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>n, ManyToOne, OneToMany, OneToOne, PrimaryGeneratedColumn } from "typeorm";
import { BranchReceive } from "./BranchReceive";
import { Product } from "./Product";
import { StockTransferSlipLotDetails } from "./StockTransferSlipLotDetails";
@Entity()
export class BranchReceiveDetails {
  @PrimaryGeneratedColumn()
  id!: number;

  @ManyToOne(() => BranchReceive, { onDelete: "CASCADE" })
  @JoinColumn()
  br!: BranchReceive;

  @ManyToOne(() => StockTransferSlipLotDetails)
  @JoinColumn()
  lot_details!: StockTransferSlipLotDetails;

  @ManyToOne(() => Product, { onDelete: "CASCADE" })
  @JoinColumn()
  product!: Product;

  @Column({ type: 'numeric' })
  receive_quantity!: number;

  @Column({ type: 'text' })
  lot_number!: string;

}