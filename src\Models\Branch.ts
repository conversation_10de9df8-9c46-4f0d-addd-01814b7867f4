import { En<PERSON>ty, PrimaryGeneratedColumn, Column } from "typeorm";

@Entity()
export class Branch {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: "text" })
  name: string;

  @Column({ type: "text" })
  address?: string;

  @Column({ type: "text", default: '' })
  contact_name!: string; // ผู้ติดต่อประจำสาขา

  @Column({ type: "text", nullable: true })
  contact_phone!: string;

  constructor() {
    this.name = "";
  }
}
