import express, { Request, Response } from "express";
import bodyParser from "body-parser";
import cors from "cors";
import path from "path";
import { AppDataSource } from "./Config/db";

// Routes
import supplierTypeRouter from "./Routes/suppliertype";
import supplierRouter from "./Routes/supplier";
import productGroupRouter from "./Routes/productgroup";
import productRouter from "./Routes/product";
import stockRoutes from "./Routes/stock";
import branchRoutes from "./Routes/branch";
import userRoutes from "./Routes/user";
import PurchaseOrderRoutes from "./Routes/purchaseorder";
import PurchaseOrderItemRoutes from "./Routes/purchaseorderitem";
import SpecialReportGroupRoutes from "./Routes/specialreportgroup";
import authRoutes from "./Routes/auth";
import attendanceRoutes from "./Routes/attendance";
import leaveRequestRoutes from "./Routes/leaveRequest";
import workingScheduleRoutes from "./Routes/workingSchedule";
import productConversionRouter from "./Routes/productConversion";
import stocktransferorderRoute from "./Routes/stocktransferorder";
import StockTransferOrderDetailsRoute from "./Routes/stocktransferorderdetails";
import StockTransferSlipRoute from "./Routes/stocktransferslip";
import StockTransferSlipDetailsRoute from "./Routes/stocktransferslipdetails";
import StockTransferSlipLotDetailsRoute from "./Routes/stocktransfersliplotdetails";
import BranchReceiveRoute from "./Routes/branchreceive";
// import ProductUnitRoute from "./Routes/productunit";
import GoodsReceiptRoute from "./Routes/goodsreceipt";
import GoodsReceiptDetailsRoute from "./Routes/goodsreceiptdetails";
import PaymentGoodsReceiptRoute from "./Routes/paymentgoodsreceipt";
import dashboardRoutes from "./Routes/dashboard";

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json({ limit: "10mb" }));
app.use(bodyParser.urlencoded({ limit: "10mb", extended: true }));

// Static files
app.use("/images", express.static(path.join(__dirname, "../images")));
console.log("Images directory path:", path.join(__dirname, "../images"));

// Routes
app.use("/suppliertypes", supplierTypeRouter);
app.use("/supplier", supplierRouter);
app.use("/productgroup", productGroupRouter);
app.use("/product", productRouter);
app.use("/stock", stockRoutes);
app.use("/branch", branchRoutes);
app.use("/user", userRoutes);
app.use("/purchaseorder", PurchaseOrderRoutes);
app.use("/purchaseorderitem", PurchaseOrderItemRoutes);
app.use("/specialreportgroup", SpecialReportGroupRoutes);
app.use("/auth", authRoutes);
app.use("/attendance", attendanceRoutes);
app.use("/leave-request", leaveRequestRoutes);
app.use("/working-schedule", workingScheduleRoutes);
app.use("/product-conversion", productConversionRouter);
app.use("/sto", stocktransferorderRoute);
app.use("/sto-details", StockTransferOrderDetailsRoute);
app.use("/sts", StockTransferSlipRoute);
app.use("/sts-details", StockTransferSlipDetailsRoute);
app.use("/br", BranchReceiveRoute);
// app.use("/product-units", ProductUnitRoute);
app.use("/gr", GoodsReceiptRoute);
app.use("/gr-details", GoodsReceiptDetailsRoute);
app.use("/payment-gr", PaymentGoodsReceiptRoute);
app.use("/dashboard", dashboardRoutes);
app.use("/sts-lot-details", StockTransferSlipLotDetailsRoute);

// Root endpoint
app.get("/", (req: Request, res: Response) => {
  res.send("Welcome to the Supplier Type API!");
});

// Connect to database and start server
AppDataSource.initialize()
  .then(() => {
    console.log("✅ Database connected successfully");
    app.listen(PORT, () => {
      console.log(`🚀 Server is running on http://localhost:${PORT}`);
    });
  })
  .catch((error) => console.error("❌ Error connecting to database:", error));