<template>
  <q-dialog v-model="isOpen">
    <q-card style="max-width: 1100px; width: 1100px"><q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">รายละเอียดสินค้า</div>
          <q-btn icon="close" @click="closeDialog()" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section style="overflow: auto; max-height: 80vh">
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            รายละเอียดสินค้า
          </div>
          <div class="shadow-2 container">
            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-6">
                <div class="row q-col-gutter-md">
                  <div class="col-12 col-md-3 q-mt-md" style="margin-left: 10px; text-align: start">
                    สินค้า :
                  </div>
                  <div class="col-12 col-md-2">
                    <q-input class="input-container-mini" dense borderless
                      v-model="stock.formforGR.product.product_code" />
                  </div>
                  <div class="col-12 col-md-6">
                    <q-input class="input-container" dense borderless v-model="stock.formforGR.product.product_name"
                      type="text" />
                  </div>
                </div>
                <div class="row q-col-gutter-md" style="margin-top: 10px">
                  <div class="col-12 col-md-3 q-mt-md"
                    style="white-space: nowrap; margin-left: 10px; text-align: start">
                    จำนวนคงเหลือ :
                  </div>
                  <div class="col-12 col-md-3">
                    <q-input class="input-container" v-model="stock.formforGR.remaining" dense borderless />
                  </div>

                  <div class="col-12 col-md-2 q-mt-md">หน่วยฐาน</div>
                  <div class="col-12 col-md-3">
                    <q-input class="input-container" v-model="stock.formforGR.product.unit" dense borderless />
                  </div>
                </div>
              </div>
              <div class="col-12 col-md-6">
                <div class="row q-col-gutter-md">
                  <div class="col-12 col-md-3 q-mt-md" style="text-align: start; white-space: nowrap">
                    หมายเหตุราคาทุน :
                  </div>
                  <div class="col-12 col-md-8" style="margin-left: 5px">
                    <q-input class="input-container" dense borderless v-model="stock.formforGR.product.cost_notes"
                      type="textarea" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row justify-between q-mt-sm">
          <div class="col-12 col-md-6">
            <div class="gap-container">
              <div class="text-white shadow-2 container-headerhalf row items-center" style="margin-right: 5px">
                การรับรอบปัจจุบัน
              </div>
              <div class="shadow-2 container" style="margin-right: 5px">
                <div class="q-mt-sm q-pr-md mini-container-header" style="padding: 5px; text-align: start">
                  <div style="margin-left: 20px; padding: 5px">จำนวนรับ</div>
                </div>
                <div class="col-12 col-md-8 mini-container">
                  <div class="row q-gutter-sm q-pa-sm items-start">
                    <!-- จำนวนรับ -->
                    <div class="column">
                      <div style="font-size: 13px; text-align: center">จำนวนรับ</div>
                      <q-input dense borderless class="input-container-2"
                        v-model.number="store.formGoodsReceiptDetail.receive_quantity"
                        input-style="padding-bottom: 15px" style="width: 120px" />
                    </div>
                    <div class="col-2">
                      <div class="q-mr-sm" style="font-size: 13px; text-align: center; padding-left: 10px">
                        หน่วย
                      </div>
                      <q-input dense borderless class="input-container-2"
                        v-model="store.formGoodsReceiptDetail.receive_unit" input-style="padding-bottom: 15px" />
                    </div>
                    <div class="col-3">
                      <div class="q-mr-sm" style="
                          font-size: 13px;
                          text-align: center;
                          white-space: nowrap;
                          padding-left: 10px;
                        ">
                        รายละเอียดหน่วย
                      </div>
                      <q-input dense borderless class="input-container-2"
                        v-model="store.formGoodsReceiptDetail.receive_unit" input-style="padding-bottom: 15px" />
                    </div>
                    <!-- ปุ่มเปลี่ยนหน่วย -->
                    <div>
                      <q-btn class="btn-add-2 q-mt-sm" label="เปลี่ยนหน่วย" @click="OpenChangeUnit" />
                    </div>
                  </div>
                </div>
                <div class="q-mt-sm q-pr-md text-center mini-container-header"
                  style="padding: 5px; text-align: start; margin-top: 10px">
                  <div>
                    <div style="margin-left: 30px">
                      ราคารับ
                      <q-checkbox v-model="store.formGoodsReceiptDetail.has_discount" label="สินค้ามีส่วนลด"
                        color="blue-9" size="xs" style="margin-left: 10px; font-size: 13px; color: #294888" />
                    </div>
                  </div>
                </div>
                <div class="col-12 col-md-8 mini-container">
                  <div class="row q-gutter-sm q-pa-sm items-start">
                    <!-- ราคารับ -->
                    <div class="column">
                      <div style="font-size: 13px; text-align: center; margin-left: 10px">
                        <span>ราคารับ</span>
                        <span style="font-size: 10px; margin-left: 4px; color: #b53638">(ไม่รวมภาษี)</span>
                      </div>
                      <q-input dense borderless class="input-container-2"
                        v-model="store.formGoodsReceiptDetail.receive_price_before_tax"
                        input-style="padding-bottom: 15px" style="width: 120px" />
                    </div>
                    <div class="col-3" style="margin-left: 10px">
                      <div style="font-size: 13px; text-align: center; margin-left: 10px">
                        <span>รวมเงิน</span>
                        <span style="font-size: 10px; margin-left: 4px; color: #b53638">(หลังลด)</span>
                      </div>
                      <q-input dense borderless class="input-container-2" v-model="totalReceiveAfterTax"
                        input-style="padding-bottom: 15px" />
                    </div>
                  </div>
                </div>
                <div class="q-mt-sm q-pr-md text-center mini-container-header"
                  style="padding: 5px; text-align: start; margin-top: 10px">
                  <div>
                    <q-checkbox v-model="store.formGoodsReceiptDetail.has_free" label="จำนวนแถม" color="blue-9"
                      size="xs" />
                  </div>
                </div>
                <div class="col-12 col-md-8 mini-container">
                  <div class="row q-gutter-sm q-pa-sm items-start">
                    <!-- จำนวนแถม -->
                    <div class="column">
                      <div style="font-size: 13px; text-align: center">จำนวนแถม</div>
                      <q-input dense borderless class="input-container-2"
                        v-model.number="store.formGoodsReceiptDetail.free_quantity" input-style="padding-bottom: 15px"
                        style="width: 120px" />
                    </div>
                    <div class="col-2" style="margin-left: 10px">
                      <div class="q-mr-sm" style="font-size: 13px; text-align: center; padding-left: 10px">
                        หน่วย
                      </div>
                      <q-input dense borderless class="input-container-2"
                        v-model="store.formGoodsReceiptDetail.free_unit" input-style="padding-bottom: 15px" />
                    </div>
                    <div class="col-3" style="margin-left: 10px">
                      <div class="q-mr-sm" style="
                          font-size: 13px;
                          text-align: center;
                          white-space: nowrap;
                          padding-left: 10px;
                        ">
                        รายละเอียดหน่วย
                      </div>
                      <q-input dense borderless class="input-container-2"
                        v-model="store.formGoodsReceiptDetail.free_unit" input-style="padding-bottom: 15px" />
                    </div>
                    <!-- ปุ่มเปลี่ยนหน่วย -->
                    <div>
                      <q-btn class="btn-add-2 q-mt-sm" label="เปลี่ยนหน่วย" @click="OpenChangeUnit" />
                    </div>
                  </div>
                </div>
                <div class="q-mt-sm q-pr-md mini-container-header"
                  style="padding: 5px; text-align: start; margin-top: 10px">
                  <q-checkbox v-model="store.formGoodsReceiptDetail.mfg_date" label="ข้อมูลการผลิต" color="blue-9"
                    size="xs" />
                </div>
                <div class="col-12 col-md-8 mini-container">
                  <div class="row q-gutter-sm q-pa-sm items-start">
                    <!-- ข้อมูลการผลิต -->
                    <div class="column">
                      <div style="font-size: 13px; text-align: center; margin-top: 5px">
                        เลขที่ผลิต
                      </div>
                      <q-input dense borderless class="input-container-2"
                        v-model="store.formGoodsReceiptDetail.production_number" input-style="padding-bottom: 15px"
                        style="width: 120px; margin-top: 5px" />
                    </div>
                    <div class="col-3" style="margin-left: 10px">
                      <div class="q-mr-sm" style="font-size: 13px; text-align: center">
                        <q-checkbox v-model="store.formGoodsReceiptDetail.mfg_date" label="ผลิต" color="blue-9"
                          size="xs" />
                      </div>

                      <q-input dense borderless class="input-container-2" v-model="formattedMfgDate"
                        input-style="padding-bottom: 15px" />
                    </div>
                    <div class="col-3" style="margin-left: 10px">
                      <div class="q-mr-sm" style="font-size: 13px; text-align: center">
                        <q-checkbox v-model="store.formGoodsReceiptDetail.exp_date" label="หมดอายุ" color="blue-9"
                          size="xs" />
                      </div>
                      <q-input dense borderless class="input-container-2" v-model="formattedExpDate"
                        input-style="padding-bottom: 15px" />
                    </div>
                  </div>
                </div>
                <div class="q-mt-sm q-pr-md mini-container-header" style="padding: 5px; text-align: start">
                  <div style="margin-left: 20px; padding: 5px">ราคาทุน</div>
                </div>
                <div class="col-12 col-md-8 mini-container">
                  <div class="row q-gutter-sm q-pa-sm items-start">
                    <!-- ราคาทุน -->
                    <div class="column">
                      <div style="font-size: 13px; text-align: center">รวมจำนวนรับ</div>
                      <q-input dense borderless class="input-container-2" v-model.number="totalReceiveQty"
                        input-style="padding-bottom: 15px" style="width: 120px" />
                    </div>
                    <div class="col-2">
                      <div class="q-mr-sm" style="font-size: 13px; text-align: center; padding-left: 10px">
                        หน่วย
                      </div>
                      <q-input dense borderless class="input-container-2"
                        v-model="store.formGoodsReceiptDetail.receive_unit" input-style="padding-bottom: 15px" />
                    </div>
                    <div class="col-3">
                      <div class="q-mr-sm" style="
                          font-size: 13px;
                          text-align: center;
                          white-space: nowrap;
                          padding-left: 10px;
                        ">
                        ราคาทุน / หน่วย
                      </div>
                      <q-input dense borderless class="input-container-2"
                        v-model="store.formGoodsReceiptDetail.cost_unit" input-style="padding-bottom: 15px" />
                    </div>
                  </div>
                </div>
                <q-card-actions align="center">
                  <q-btn class="btn-accept-2" dense flat label="บันทึก" style="margin-top: 10px"
                    @click="saveDetailsDialog" />
                  <q-btn class="btn-cancel" dense flat label="ยกเลิก" style="margin-top: 10px" @click="closeDialog" />
                </q-card-actions>
              </div>
            </div>
          </div>

          <div class="col-12 col-md-6">
            <div class="gap-container">
              <div class="text-white shadow-2 container-headerhalf row items-center" style="margin-left: 5px">
                การรับรอบก่อนหน้า
                <q-input class="input-container-mini flex flex-center" dense borderless
                  v-model="store.formLatestGRDetail.lot_number_before"
                  style="margin-left: 10px; width: 150px; height: 30px" input-style="padding-bottom: 15px" readonly />
              </div>

              <div class="shadow-2 container" style="margin-left: 5px">
                <div class="q-mt-sm q-pr-md mini-container-header" style="padding: 5px; text-align: start">
                  <div style="margin-left: 20px; padding: 5px">จำนวนรับ</div>
                </div>
                <div class="col-12 col-md-8 mini-container">
                  <div class="row q-gutter-sm q-pa-sm items-start">
                    <!-- จำนวนรับ -->
                    <div class="column">
                      <div style="font-size: 13px; text-align: center">จำนวนรับ</div>
                      <q-input dense borderless class="input-container-2"
                        v-model.number="store.formLatestGRDetail.receive_quantity" input-style="padding-bottom: 15px"
                        style="width: 120px" readonly />
                    </div>
                    <div class="col-2">
                      <div class="q-mr-sm" style="font-size: 13px; text-align: center; padding-left: 10px">
                        หน่วย
                      </div>
                      <q-input dense borderless class="input-container-2"
                        v-model="store.formLatestGRDetail.receive_unit" input-style="padding-bottom: 15px" readonly />
                    </div>
                    <div class="col-3">
                      <div class="q-mr-sm" style="
                          font-size: 13px;
                          text-align: center;
                          white-space: nowrap;
                          padding-left: 10px;
                        ">
                        รายละเอียดหน่วย
                      </div>
                      <q-input dense borderless class="input-container-2"
                        v-model="store.formLatestGRDetail.receive_unit" input-style="padding-bottom: 15px" readonly />
                    </div>
                    <!-- ปุ่มเปลี่ยนหน่วย -->
                    <div>
                      <q-btn class="btn-add-2 q-mt-sm" label="เปลี่ยนหน่วย" @click="OpenChangeUnit" disabled />
                    </div>
                  </div>
                </div>
                <div class="q-mt-sm q-pr-md text-center mini-container-header"
                  style="padding: 5px; text-align: start; margin-top: 10px">
                  <div>
                    <div style="margin-left: 30px">
                      ราคารับ
                      <q-checkbox v-model="store.formLatestGRDetail.receive_price_after_discount" label="สินค้ามีส่วนลด"
                        color="blue-9" size="xs" style="margin-left: 10px; font-size: 13px; color: #294888" />
                    </div>
                  </div>
                </div>
                <div class="col-12 col-md-8 mini-container">
                  <div class="row q-gutter-sm q-pa-sm items-start">
                    <!-- ราคารับ -->
                    <div class="column">
                      <div style="font-size: 13px; text-align: center; margin-left: 10px">
                        <span>ราคารับ</span>
                        <span style="font-size: 10px; margin-left: 4px; color: #b53638">(ไม่รวมภาษี)</span>
                      </div>
                      <q-input dense borderless class="input-container-2"
                        v-model="store.formLatestGRDetail.receive_price_before_tax" input-style="padding-bottom: 15px"
                        style="width: 120px" readonly />
                    </div>
                    <div class="col-3" style="margin-left: 10px">
                      <div style="font-size: 13px; text-align: center; margin-left: 10px">
                        <span>รวมเงิน</span>
                        <span style="font-size: 10px; margin-left: 4px; color: #b53638">(หลังลด)</span>
                      </div>
                      <q-input dense borderless class="input-container-2"
                        v-model="store.formLatestGRDetail.receive_price_after_discount"
                        input-style="padding-bottom: 15px" readonly />
                    </div>
                  </div>
                </div>
                <div class="q-mt-sm q-pr-md text-center mini-container-header"
                  style="padding: 5px; text-align: start; margin-top: 10px">
                  <div>
                    <q-checkbox v-model="store.formLatestGRDetail.has_free" label="จำนวนแถม" color="blue-9" size="xs" />
                  </div>
                </div>
                <div class="col-12 col-md-8 mini-container">
                  <div class="row q-gutter-sm q-pa-sm items-start">
                    <!-- จำนวนแถม -->
                    <div class="column">
                      <div style="font-size: 13px; text-align: center">จำนวนแถม</div>
                      <q-input dense borderless class="input-container-2"
                        v-model.number="store.formLatestGRDetail.free_quantity" input-style="padding-bottom: 15px"
                        style="width: 120px" readonly />
                    </div>
                    <div class="col-2" style="margin-left: 10px">
                      <div class="q-mr-sm" style="font-size: 13px; text-align: center; padding-left: 10px">
                        หน่วย
                      </div>
                      <q-input dense borderless class="input-container-2" v-model="store.formLatestGRDetail.free_unit"
                        input-style="padding-bottom: 15px" readonly />
                    </div>
                    <div class="col-3" style="margin-left: 10px">
                      <div class="q-mr-sm" style="
                          font-size: 13px;
                          text-align: center;
                          white-space: nowrap;
                          padding-left: 10px;
                        ">
                        รายละเอียดหน่วย
                      </div>
                      <q-input dense borderless class="input-container-2" v-model="store.formLatestGRDetail.free_unit"
                        input-style="padding-bottom: 15px" readonly />
                    </div>
                    <!-- ปุ่มเปลี่ยนหน่วย -->
                    <div>
                      <q-btn class="btn-add-2 q-mt-sm" label="เปลี่ยนหน่วย" @click="OpenChangeUnit" disabled />
                    </div>
                  </div>
                </div>
                <div class="q-mt-sm q-pr-md mini-container-header"
                  style="padding: 5px; text-align: start; margin-top: 10px">
                  <q-checkbox v-model="store.formLatestGRDetail.mfg_date" label="ข้อมูลการผลิต" color="blue-9"
                    size="xs" />
                </div>
                <div class="col-12 col-md-8 mini-container">
                  <div class="row q-gutter-sm q-pa-sm items-start">
                    <!-- ข้อมูลการผลิต -->
                    <div class="column">
                      <div style="font-size: 13px; text-align: center; margin-top: 5px">
                        เลขที่ผลิต
                      </div>
                      <q-input dense borderless class="input-container-2"
                        v-model="store.formLatestGRDetail.production_number" input-style="padding-bottom: 15px"
                        style="width: 120px; margin-top: 5px" readonly />
                    </div>
                    <div class="col-3" style="margin-left: 10px">
                      <div class="q-mr-sm" style="font-size: 13px; text-align: center">
                        <q-checkbox v-model="store.formLatestGRDetail.mfg_date" label="ผลิต" color="blue-9" size="xs" />
                      </div>

                      <q-input dense borderless class="input-container-2" v-model="formattedLastestMfgDate"
                        input-style="padding-bottom: 15px" readonly />
                    </div>
                    <div class="col-3" style="margin-left: 10px">
                      <div class="q-mr-sm" style="font-size: 13px; text-align: center">
                        <q-checkbox v-model="store.formLatestGRDetail.exp_date" label="หมดอายุ" color="blue-9"
                          size="xs" />
                      </div>
                      <q-input dense borderless class="input-container-2" v-model="formattedLastestExpDate"
                        input-style="padding-bottom: 15px" readonly />
                    </div>
                  </div>
                </div>
                <div class="q-mt-sm q-pr-md mini-container-header" style="padding: 5px; text-align: start">
                  <div style="margin-left: 20px; padding: 5px">ราคาทุน</div>
                </div>
                <div class="col-12 col-md-8 mini-container">
                  <div class="row q-gutter-sm q-pa-sm items-start">
                    <!-- ราคาทุน -->
                    <div class="column">
                      <div style="font-size: 13px; text-align: center">รวมจำนวนรับ</div>
                      <q-input dense borderless class="input-container-2"
                        v-model.number="store.formLatestGRDetail.total_receive_quantity"
                        input-style="padding-bottom: 15px" style="width: 120px" readonly />
                    </div>
                    <div class="col-2">
                      <div class="q-mr-sm" style="font-size: 13px; text-align: center; padding-left: 10px">
                        หน่วย
                      </div>
                      <q-input dense borderless class="input-container-2"
                        v-model="store.formLatestGRDetail.receive_unit" input-style="padding-bottom: 15px" readonly />
                    </div>
                    <div class="col-3">
                      <div class="q-mr-sm" style="
                          font-size: 13px;
                          text-align: center;
                          white-space: nowrap;
                          padding-left: 10px;
                        ">
                        ราคาทุน / หน่วย
                      </div>
                      <q-input dense borderless class="input-container-2" v-model="store.formLatestGRDetail.cost_unit"
                        input-style="padding-bottom: 15px" readonly />
                    </div>
                  </div>
                </div>
                <div style="margin: 55px"></div>
              </div>
            </div>
          </div>
          <ChangeUnitDialog></ChangeUnitDialog>
        </div>
      </q-card-section></q-card></q-dialog>
</template>
<script setup lang="ts">
import { useDialogGRDetails } from 'src/stores/dialog-gr-details'
import { useGoodsReceiptStore } from 'src/stores/goodsreceipt'
import { useStockStore } from 'src/stores/stock'
import ChangeUnitDialog from './ChangeUnitDialog.vue'

import { computed, watch } from 'vue'
import { date } from 'quasar'
import type { GoodsReceiptDetail } from 'src/types/goodsReceiptDatail'

const dialogPD = useDialogGRDetails()
const stock = useStockStore()
const store = useGoodsReceiptStore()
interface Props {
  modelValue: boolean
  grDetails?: GoodsReceiptDetail | null
  mode?: string
}

const props = withDefaults(defineProps<Props>(), {
  grDetails: null,
  mode: 'edit'
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'item-updated': []
}>()

const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

watch(
  () => props.modelValue,
  () => {
    if (props.mode === 'edit' || props.mode === 'addProductList') {
      // store.grDetailsEdited = store.grDetails
    }
  }
)
const totalReceiveQty = computed(() => {
  const sum = store.formGoodsReceiptDetail.receive_quantity + store.formGoodsReceiptDetail.free_quantity;
  return sum
});
const totalReceiveAfterTax = computed(() => {
  const sum = store.formGoodsReceiptDetail.receive_quantity * store.formGoodsReceiptDetail.receive_price_before_tax;
  return Math.round(sum * 10000) / 10000;
});
const saveDetailsDialog = () => {
  store.formGoodsReceiptDetail.total_receive_quantity = totalReceiveQty.value
  store.formGoodsReceiptDetail.receive_price_after_discount = totalReceiveAfterTax.value
  store.formGoodsReceiptDetail.total_price_product = totalReceiveAfterTax.value
  // console.log(store.formGoodsReceiptDetail)
  const index = store.grDetails.findIndex(item => item.product.id === store.formGoodsReceiptDetail.product.id);

  if (props.mode === 'edit') {
    if (index !== -1) {
      store.grDetails[index] = {
        ...store.grDetails[index],
        ...store.formGoodsReceiptDetail
      };
    } else {
      store.grDetails.push(store.formGoodsReceiptDetail)
    }
    store.deletedIds = store.deletedIds.filter(id => id !== store.formGoodsReceiptDetail.product.id);
  } else {
    const exists = store.grDetailsEdited.some(item => item.product.id === store.formGoodsReceiptDetail.product.id);
    if (!exists) {
      store.grDetailsEdited.push(store.formGoodsReceiptDetail);
    }
    // else{
    //   store.grDetailsEdited[index] = {
    //     ...originalItem,
    //     ...copiedFormOrderItems,
    //     id: oldId, // ป้องกัน id หาย
    //     quantity: updatedQuantity
    //   };
    // }
  }
  store.form.gr_details_total = store.grDetails.reduce((acc, item) => acc + (Number(item.total_price_product) || 0), 0)
  store.formGoodsReceiptDetail.gr_total = store.grDetails.reduce((acc, item) => acc + (Number(item.total_price_product) || 0), 0)
  isOpen.value = false
  store.resetFormGRDetails()
  store.resetFormLatestGRDetail()
}
const closeDialog = () => {
  isOpen.value = false
  store.resetFormGRDetails()
  store.resetFormLatestGRDetail()
}

function OpenChangeUnit() {
  dialogPD.openCU('addCU')
  console.log('เปิดหน้าเปลี่ยนหน่วย')
}
const formattedMfgDate = computed({
  get() {
    const dateValue = store.formGoodsReceiptDetail.mfg_date;
    return dateValue ? date.formatDate(dateValue, 'DD/MM/YYYY') : '';
  },
  set(value: string) {
    // ถ้าค่า value เป็น string ว่าง แสดงว่าให้เคลียร์ค่า
    if (value === '') {
      store.formGoodsReceiptDetail.mfg_date = new Date(''); // หรือ undefined ก็ได้ ถ้า model รองรับ
    } else {
      store.formGoodsReceiptDetail.mfg_date = new Date(value);
    }
  },
});
const formattedExpDate = computed({
  get() {
    const dateValue = store.formGoodsReceiptDetail.exp_date;
    return dateValue ? date.formatDate(dateValue, 'DD/MM/YYYY') : '';
  },
  set(value: string) {
    // ถ้าค่า value เป็น string ว่าง แสดงว่าให้เคลียร์ค่า
    if (value === '') {
      store.formGoodsReceiptDetail.exp_date = new Date(''); // หรือ undefined ก็ได้ ถ้า model รองรับ
    } else {
      store.formGoodsReceiptDetail.exp_date = new Date(value);
    }
  },
});
const formattedLastestExpDate = computed({
  get() {
    const dateValue = store.formLatestGRDetail.exp_date;
    return dateValue ? date.formatDate(dateValue, 'DD/MM/YYYY') : '';
  },
  set(value: string) {
    // ถ้าค่า value เป็น string ว่าง แสดงว่าให้เคลียร์ค่า
    if (value === '') {
      store.formLatestGRDetail.exp_date = new Date(''); // หรือ undefined ก็ได้ ถ้า model รองรับ
    } else {
      store.formLatestGRDetail.exp_date = new Date(value);
    }
  },
});
const formattedLastestMfgDate = computed({
  get() {
    const dateValue = store.formLatestGRDetail.mfg_date;
    return dateValue ? date.formatDate(dateValue, 'DD/MM/YYYY') : '';
  },
  set(value: string) {
    // ถ้าค่า value เป็น string ว่าง แสดงว่าให้เคลียร์ค่า
    if (value === '') {
      store.formLatestGRDetail.mfg_date = new Date(''); // หรือ undefined ก็ได้ ถ้า model รองรับ
    } else {
      store.formLatestGRDetail.mfg_date = new Date(value);
    }
  },
});
</script>
<style scoped>
.gap-container {
  margin-bottom: 20px;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
  font-size: 15px;
}

.input-container-2 {
  padding-left: 5px;
  padding-right: 5px;
  background-color: #d9d9d9;
  border-radius: 5px;
  font-size: 13px;
  height: 30px;
  margin-left: 5px;
}

.input-container-mini {
  padding-left: 10px;
  padding-right: 10px;
  background-color: #a4cafe;
  border-radius: 5px;
  display: flex;
  align-items: center;
  /* จัดกลางแนวตั้ง */
  justify-content: center;
  /* จัดกลางแนวนอน */
}

.mini-container-header {
  background-color: #83a7d8;
  border-radius: 5px 5px 0 0;
  width: 490px;
  height: 45px;
}

.width-column {
  width: 100px;
}

.container-headerhalf {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.mini-container {
  background-color: white;
  border-radius: 0 0 5px 5px;
  width: 490px;
  padding-bottom: 10px;
}

.btn-add-2 {
  background-color: #294888;
  color: white;
  border-radius: 3px;
  margin-left: 10px;
  font-size: 13px;
  margin-top: 17px;
}
</style>
