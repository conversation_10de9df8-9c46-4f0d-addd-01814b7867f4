import { Request, Response } from "express";
import { AppDataSource } from "../../Config/db";
import { Attendance } from "../../Models/Attendance";
import { User } from "../../Models/User";
import { Between, Not, IsNull } from "typeorm";
import { LeaveRequest } from "../../Models/LeaveRequest";
import { WorkingScheduleController } from "./WorkingSchedule";
import {
  LocationService,
  LocationCoordinates,
} from "../../Services/LocationService";

export class AttendanceController {
  // Helper method to determine attendance status based on working schedule
  private async determineAttendanceStatus(
    userId: number,
    clockInTime: string
  ): Promise<string> {
    try {
      // Get user's working schedule
      const schedule = await WorkingScheduleController.getUserActiveSchedule(
        userId
      );

      if (!schedule) {
        // Fallback to default 9:00 AM if no schedule found
        const [hours, minutes] = clockInTime.split(":").map(Number);
        const clockInMinutes = hours * 60 + minutes;
        const defaultCutoffTime = 9 * 60; // 09:00 AM in minutes
        return clockInMinutes > defaultCutoffTime ? "Late" : "Present";
      }

      // Parse schedule times
      const [scheduleHours, scheduleMinutes] = schedule.standard_check_in_time
        .split(":")
        .map(Number);
      const scheduledCheckInMinutes = scheduleHours * 60 + scheduleMinutes;

      // Parse actual clock-in time
      const [actualHours, actualMinutes] = clockInTime.split(":").map(Number);
      const actualClockInMinutes = actualHours * 60 + actualMinutes;

      // Calculate cutoff time with late threshold
      const cutoffTime =
        scheduledCheckInMinutes + schedule.late_threshold_minutes;

      // Determine status
      if (actualClockInMinutes <= scheduledCheckInMinutes) {
        return "Present";
      } else if (actualClockInMinutes <= cutoffTime) {
        return "Present"; // Within grace period
      } else {
        return "Late";
      }
    } catch (error) {
      console.error("Error determining attendance status:", error);
      // Fallback to default logic
      const [hours, minutes] = clockInTime.split(":").map(Number);
      const clockInMinutes = hours * 60 + minutes;
      const defaultCutoffTime = 9 * 60; // 09:00 AM in minutes
      return clockInMinutes > defaultCutoffTime ? "Late" : "Present";
    }
  }

  // Helper method to determine if an attendance record is late based on user's schedule
  private async isAttendanceLate(
    userId: number,
    clockInTime: string
  ): Promise<boolean> {
    try {
      const schedule = await WorkingScheduleController.getUserActiveSchedule(
        userId
      );

      if (!schedule) {
        // Fallback to default 9:05 AM if no schedule found
        const [hours, minutes] = clockInTime.split(":").map(Number);
        const clockInMinutes = hours * 60 + minutes;
        const defaultCutoffTime = 9 * 60 + 5; // 09:05 AM in minutes
        return clockInMinutes > defaultCutoffTime;
      }

      // Parse schedule times
      const [scheduleHours, scheduleMinutes] = schedule.standard_check_in_time
        .split(":")
        .map(Number);
      const scheduledCheckInMinutes = scheduleHours * 60 + scheduleMinutes;

      // Parse actual clock-in time
      const [actualHours, actualMinutes] = clockInTime.split(":").map(Number);
      const actualClockInMinutes = actualHours * 60 + actualMinutes;

      // Calculate cutoff time with late threshold
      const cutoffTime =
        scheduledCheckInMinutes + schedule.late_threshold_minutes;

      return actualClockInMinutes > cutoffTime;
    } catch (error) {
      console.error("Error checking if attendance is late:", error);
      // Fallback to default logic
      const [hours, minutes] = clockInTime.split(":").map(Number);
      const clockInMinutes = hours * 60 + minutes;
      const defaultCutoffTime = 9 * 60 + 5; // 09:05 AM in minutes
      return clockInMinutes > defaultCutoffTime;
    }
  }

  public async clockIn(req: Request, res: Response): Promise<void> {
    const attendanceRepository = AppDataSource.getRepository(Attendance);
    const userRepository = AppDataSource.getRepository(User);

    try {
      const { userId, note, location } = req.body;

      if (!userId) {
        res.status(400).json({ message: "userId is required" });
        return;
      }

      // Validate location if provided
      if (location) {
        if (!LocationService.isValidCoordinates(location)) {
          res.status(400).json({ message: "Invalid location coordinates" });
          return;
        }

        const locationValidation = LocationService.validateLocation(location);
        if (!locationValidation.isValid) {
          res.status(400).json({
            message: "Location validation failed",
            details: locationValidation.message,
            distance: locationValidation.distance,
            maxAllowedDistance: LocationService.getMaxAllowedDistance(),
          });
          return;
        }
      }

      const user = await userRepository.findOne({
        where: { id: Number(userId) },
      });
      if (!user) {
        res.status(404).json({ message: "User not found" });
        return;
      }

      const today = new Date().toISOString().split("T")[0];
      const existingAttendance = await attendanceRepository.findOne({
        where: {
          user: { id: userId },
          date: today,
        },
        relations: ["user"],
      });

      if (existingAttendance) {
        res.status(400).json({ message: "Already clocked in today" });
        return;
      }

      const currentTime = new Date().toTimeString().split(" ")[0];

      // Determine attendance status based on user's working schedule
      const status = await this.determineAttendanceStatus(userId, currentTime);

      const newAttendance = attendanceRepository.create({
        user,
        date: today,
        clock_in: currentTime,
        note,
        status: status,
        // Add location data if provided
        check_in_latitude: location?.latitude,
        check_in_longitude: location?.longitude,
        check_in_accuracy: location?.accuracy,
      });

      const savedAttendance = await attendanceRepository.save(newAttendance);

      // Clean up response data
      const { user: attendanceUser, ...attendanceData } = savedAttendance;
      res.status(201).json({
        ...attendanceData,
        user: {
          id: attendanceUser.id,
          name: attendanceUser.name,
        },
      });
    } catch (error) {
      console.error("Clock-in error:", error);
      res.status(500).json({ message: "Server error during clock-in" });
    }
  }

  public async clockOut(req: Request, res: Response): Promise<void> {
    const attendanceRepository = AppDataSource.getRepository(Attendance);
    const userRepository = AppDataSource.getRepository(User);

    try {
      const { userId, location } = req.body;

      if (!userId) {
        res.status(400).json({ message: "userId is required" });
        return;
      }

      // Validate location if provided
      if (location) {
        if (!LocationService.isValidCoordinates(location)) {
          res.status(400).json({ message: "Invalid location coordinates" });
          return;
        }

        const locationValidation = LocationService.validateLocation(location);
        if (!locationValidation.isValid) {
          res.status(400).json({
            message: "Location validation failed",
            details: locationValidation.message,
            distance: locationValidation.distance,
            maxAllowedDistance: LocationService.getMaxAllowedDistance(),
          });
          return;
        }
      }

      const today = new Date().toISOString().split("T")[0];
      const attendance = await attendanceRepository.findOne({
        where: {
          user: { id: userId },
          date: today,
        },
        relations: ["user"],
      });

      if (!attendance) {
        res.status(404).json({ message: "No clock-in record found for today" });
        return;
      }

      if (attendance.clock_out) {
        res.status(400).json({ message: "Already clocked out today" });
        return;
      }

      const clockOutTime = new Date().toTimeString().split(" ")[0];
      attendance.clock_out = clockOutTime;

      // Add location data if provided
      if (location) {
        attendance.check_out_latitude = location.latitude;
        attendance.check_out_longitude = location.longitude;
        attendance.check_out_accuracy = location.accuracy;
      }

      // Calculate work duration for today
      const clockInTime = new Date(`${today}T${attendance.clock_in}`);
      const clockOutDateTime = new Date(`${today}T${clockOutTime}`);
      const workDuration = Number(
        (
          (clockOutDateTime.getTime() - clockInTime.getTime()) /
          (1000 * 60 * 60)
        ).toFixed(2)
      );
      attendance.work_duration = workDuration;

      // Save the attendance first
      const updatedAttendance = await attendanceRepository.save(attendance);

      // Get user and update hour_work by adding the new work_duration
      const user = await userRepository.findOne({
        where: { id: userId },
      });

      if (user) {
        // Simply add the new work_duration to existing hour_work
        const newHourWork = Number((user.hour_work + workDuration).toFixed(2));
        user.hour_work = newHourWork;
        await userRepository.save(user);
      }

      // Clean up response data
      const { user: attendanceUser, ...attendanceData } = updatedAttendance;
      res.status(200).json({
        ...attendanceData,
        user: {
          id: attendanceUser.id,
          name: attendanceUser.name,
          hour_work: user?.hour_work,
        },
      });
    } catch (error) {
      console.error("Clock-out error:", error);
      res.status(500).json({ message: "Server error during clock-out" });
    }
  }

  public async getUserAttendance(req: Request, res: Response): Promise<void> {
    const attendanceRepository = AppDataSource.getRepository(Attendance);
    const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);

    try {
      const userId = Number(req.params.userId);
      const { startDate, endDate } = req.query;

      if (!userId) {
        res.status(400).json({ message: "userId is required" });
        return;
      }

      let whereClause: any = { user: { id: userId } };
      let leaveWhereClause: any = { user: { id: userId } };

      if (startDate && endDate) {
        whereClause.date = Between(startDate as string, endDate as string);
        leaveWhereClause.leave_date = Between(
          startDate as string,
          endDate as string
        );
      }

      const attendances = await attendanceRepository.find({
        where: whereClause,
        relations: ["user"],
        order: {
          date: "DESC",
        },
      });

      // Get leave requests for the same user and date range
      const leaveRequests = await leaveRequestRepository.find({
        where: leaveWhereClause,
        relations: ["user"],
        order: {
          leave_date: "DESC",
        },
      });

      // Clean up attendance data
      const cleanedAttendances = attendances.map((attendance) => {
        const { user, ...attendanceData } = attendance;
        return {
          ...attendanceData,
          user: {
            id: user.id,
            name: user.name,
          },
        };
      });

      // Format leave requests to match attendance structure
      const formattedLeaves = leaveRequests.map((leave) => {
        const { user, ...leaveData } = leave;
        return {
          id: leave.id,
          date: leave.leave_date,
          status: leave.leave_type === "sick" ? "Sick Leave" : "Personal Leave",
          note: leave.reason || null,
          clock_in: null,
          clock_out: null,
          work_duration: 0,
          is_leave: true,
          leave_type: leave.leave_type,
          user: {
            id: user.id,
            name: user.name,
          },
        };
      });

      // Combine both datasets and sort by date
      const combinedData = [...cleanedAttendances, ...formattedLeaves].sort(
        (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
      );

      res.status(200).json(combinedData);
    } catch (error) {
      console.error("Get attendance error:", error);
      res
        .status(500)
        .json({ message: "Server error while fetching attendance records" });
    }
  }

  public async getAllAttendanceStatus(
    req: Request,
    res: Response
  ): Promise<void> {
    const attendanceRepository = AppDataSource.getRepository(Attendance);
    const userRepository = AppDataSource.getRepository(User);

    try {
      // Get all users with their attendance records and leave counts
      const users = await userRepository.find({
        relations: ["attendances"],
        select: {
          id: true,
          name: true,
          sick_leave: true,
          personal_leave: true,
        },
      });

      const attendanceStats = await Promise.all(
        users.map(async (user) => {
          const attendances = await attendanceRepository.find({
            where: { user: { id: user.id } },
          });

          let present = 0;
          let late = 0;

          // Process each attendance record
          for (const attendance of attendances) {
            const isLate = await this.isAttendanceLate(
              user.id,
              attendance.clock_in
            );
            if (isLate) {
              late++;
            } else {
              present++;
            }
          }

          return {
            userId: user.id,
            userName: user.name,
            present,
            late,
            sickLeave: user.sick_leave,
            personalLeave: user.personal_leave,
          };
        })
      );

      res.status(200).json(attendanceStats);
    } catch (error) {
      console.error("Get attendance status error:", error);
      res.status(500).json({
        message: "Server error while fetching attendance status",
      });
    }
  }

  public async getAttendanceSummaryByRole(
    req: Request,
    res: Response
  ): Promise<void> {
    const attendanceRepository = AppDataSource.getRepository(Attendance);
    const userRepository = AppDataSource.getRepository(User);

    try {
      // Get all users with their attendance records and roles
      const users = await userRepository.find({
        relations: ["attendances"],
        select: {
          id: true,
          name: true,
          role: true,
          sick_leave: true,
          personal_leave: true,
        },
      });

      // Initialize summary structure
      const summary = {
        fullTime: {
          totalEmployees: 0,
          present: 0,
          late: 0,
          sickLeave: 0,
          personalLeave: 0,
        },
        partTime: {
          totalEmployees: 0,
          present: 0,
          late: 0,
          sickLeave: 0,
          personalLeave: 0,
        },
      };

      // Process each user's attendance
      for (const user of users) {
        // Classify users based on specific roles
        const isFullTime = user.role === "Employee" || user.role === "Manager";
        const group = isFullTime ? "fullTime" : "partTime";

        // Skip users who don't match either category
        if (!isFullTime && user.role !== "Part-time") {
          continue;
        }

        summary[group].totalEmployees++;

        // Add leave counts
        summary[group].sickLeave += user.sick_leave;
        summary[group].personalLeave += user.personal_leave;

        // Get attendance records for the user
        const attendances = await attendanceRepository.find({
          where: { user: { id: user.id } },
        });

        // Calculate present and late counts
        for (const attendance of attendances) {
          const isLate = await this.isAttendanceLate(
            user.id,
            attendance.clock_in
          );
          if (isLate) {
            summary[group].late++;
          } else {
            summary[group].present++;
          }
        }
      }

      // Calculate averages and add them to the response
      const response = {
        fullTime: {
          ...summary.fullTime,
          averages: {
            presentPerEmployee: summary.fullTime.totalEmployees
              ? (
                  summary.fullTime.present / summary.fullTime.totalEmployees
                ).toFixed(2)
              : 0,
            latePerEmployee: summary.fullTime.totalEmployees
              ? (
                  summary.fullTime.late / summary.fullTime.totalEmployees
                ).toFixed(2)
              : 0,
            sickLeavePerEmployee: summary.fullTime.totalEmployees
              ? (
                  summary.fullTime.sickLeave / summary.fullTime.totalEmployees
                ).toFixed(2)
              : 0,
            personalLeavePerEmployee: summary.fullTime.totalEmployees
              ? (
                  summary.fullTime.personalLeave /
                  summary.fullTime.totalEmployees
                ).toFixed(2)
              : 0,
          },
        },
        partTime: {
          ...summary.partTime,
          averages: {
            presentPerEmployee: summary.partTime.totalEmployees
              ? (
                  summary.partTime.present / summary.partTime.totalEmployees
                ).toFixed(2)
              : 0,
            latePerEmployee: summary.partTime.totalEmployees
              ? (
                  summary.partTime.late / summary.partTime.totalEmployees
                ).toFixed(2)
              : 0,
            sickLeavePerEmployee: summary.partTime.totalEmployees
              ? (
                  summary.partTime.sickLeave / summary.partTime.totalEmployees
                ).toFixed(2)
              : 0,
            personalLeavePerEmployee: summary.partTime.totalEmployees
              ? (
                  summary.partTime.personalLeave /
                  summary.partTime.totalEmployees
                ).toFixed(2)
              : 0,
          },
        },
      };

      res.status(200).json(response);
    } catch (error) {
      console.error("Get attendance summary by role error:", error);
      res.status(500).json({
        message: "Server error while fetching attendance summary by role",
      });
    }
  }

  public async getTotalAttendanceByRole(
    req: Request,
    res: Response
  ): Promise<void> {
    const userRepository = AppDataSource.getRepository(User);

    try {
      // Get all users with their attendance records and roles
      const users = await userRepository.find({
        relations: ["attendances"],
        select: {
          id: true,
          name: true,
          role: true,
          sick_leave: true,
          personal_leave: true,
        },
      });

      // Group users by role and calculate totals
      const roleTotals: Record<
        string,
        {
          present: number;
          late: number;
          sickLeave: number;
          personalLeave: number;
          totalEmployees: number;
        }
      > = {};

      // Process each user's attendance
      for (const user of users) {
        // Initialize role in totals if not exists
        if (!roleTotals[user.role]) {
          roleTotals[user.role] = {
            present: 0,
            late: 0,
            sickLeave: 0,
            personalLeave: 0,
            totalEmployees: 0,
          };
        }

        // Increment employee count for this role
        roleTotals[user.role].totalEmployees++;

        // Add leave counts
        roleTotals[user.role].sickLeave += user.sick_leave;
        roleTotals[user.role].personalLeave += user.personal_leave;

        // Calculate present and late counts from attendance records
        if (user.attendances) {
          for (const attendance of user.attendances) {
            const isLate = await this.isAttendanceLate(
              user.id,
              attendance.clock_in
            );
            if (isLate) {
              roleTotals[user.role].late++;
            } else {
              roleTotals[user.role].present++;
            }
          }
        }
      }

      res.status(200).json(roleTotals);
    } catch (error) {
      console.error("Get total attendance by role error:", error);
      res.status(500).json({
        message: "Server error while fetching total attendance by role",
      });
    }
  }

  public async getTotalAttendanceByCategory(
    req: Request,
    res: Response
  ): Promise<void> {
    const userRepository = AppDataSource.getRepository(User);

    try {
      // Get all users with their attendance records and roles
      const users = await userRepository.find({
        relations: ["attendances"],
        select: {
          id: true,
          name: true,
          role: true,
          sick_leave: true,
          personal_leave: true,
        },
      });

      // Initialize category totals
      const categoryTotals = {
        fullTime: {
          present: 0,
          late: 0,
          sickLeave: 0,
          personalLeave: 0,
          totalEmployees: 0,
          total: 0,
        },
        partTime: {
          present: 0,
          late: 0,
          sickLeave: 0,
          personalLeave: 0,
          totalEmployees: 0,
          total: 0,
        },
      };

      // Process each user's attendance
      for (const user of users) {
        // Classify users based on specific roles
        const isFullTime = user.role === "Employee" || user.role === "Manager";
        const category = isFullTime ? "fullTime" : "partTime";

        // Skip users who don't match either category
        if (!isFullTime && user.role !== "Part-time") {
          continue;
        }

        // Increment employee count for this category
        categoryTotals[category].totalEmployees++;

        // Add leave counts
        categoryTotals[category].sickLeave += user.sick_leave;
        categoryTotals[category].personalLeave += user.personal_leave;

        // Calculate present and late counts from attendance records
        if (user.attendances) {
          for (const attendance of user.attendances) {
            const isLate = await this.isAttendanceLate(
              user.id,
              attendance.clock_in
            );
            if (isLate) {
              categoryTotals[category].late++;
            } else {
              categoryTotals[category].present++;
            }
          }
        }
      }

      // Calculate total for each category
      categoryTotals.fullTime.total =
        categoryTotals.fullTime.present +
        categoryTotals.fullTime.late +
        categoryTotals.fullTime.sickLeave +
        categoryTotals.fullTime.personalLeave;

      categoryTotals.partTime.total =
        categoryTotals.partTime.present +
        categoryTotals.partTime.late +
        categoryTotals.partTime.sickLeave +
        categoryTotals.partTime.personalLeave;

      res.status(200).json(categoryTotals);
    } catch (error) {
      console.error("Get total attendance by category error:", error);
      res.status(500).json({
        message: "Server error while fetching total attendance by category",
      });
    }
  }

  public async getAttendanceUserTodayById(
    req: Request,
    res: Response
  ): Promise<void> {
    const attendanceRepository = AppDataSource.getRepository(Attendance);
    const userRepository = AppDataSource.getRepository(User);

    try {
      const userId = Number(req.params.userId);

      if (!userId) {
        res.status(400).json({ message: "userId is required" });
        return;
      }

      // Get user data
      const user = await userRepository.findOne({
        where: { id: userId },
      });

      if (!user) {
        res.status(404).json({ message: "User not found" });
        return;
      }

      const today = new Date().toISOString().split("T")[0];
      const attendance = await attendanceRepository.findOne({
        where: {
          user: { id: userId },
          date: today,
        },
        relations: ["user"],
      });

      if (!attendance) {
        res.status(200).json({
          clock_in: null,
          clock_out: null,
          status: null,
          user: {
            id: userId,
            name: user.name,
          },
        });
        return;
      }

      // Determine if the user was late based on their working schedule
      let status = attendance.status;
      if (attendance.clock_in) {
        status = await this.determineAttendanceStatus(
          userId,
          attendance.clock_in
        );
      }

      // Clean up response data
      const { user: attendanceUser, ...attendanceData } = attendance;
      res.status(200).json({
        ...attendanceData,
        status: status,
        user: {
          id: attendanceUser.id,
          name: attendanceUser.name,
        },
      });
    } catch (error) {
      console.error("Get today's attendance error:", error);
      res.status(500).json({
        message: "Server error while fetching today's attendance record",
      });
    }
  }

  public async getAttendanceByDateRange(
    req: Request,
    res: Response
  ): Promise<void> {
    const attendanceRepository = AppDataSource.getRepository(Attendance);
    const userRepository = AppDataSource.getRepository(User);
    const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);

    try {
      // Get startDate and endDate from either query params or request body
      const startDate = req.query.startDate || req.body.startDate;
      const endDate = req.query.endDate || req.body.endDate;

      if (!startDate || !endDate) {
        res.status(400).json({ message: "startDate and endDate are required" });
        return;
      }

      // Get all users
      const users = await userRepository.find({
        select: {
          id: true,
          name: true,
          day_off: true,
        },
      });

      // Get all attendance records within date range
      const attendances = await attendanceRepository.find({
        where: {
          date: Between(startDate as string, endDate as string),
        },
        relations: ["user"],
      });

      // Get all leave requests within date range
      const leaveRequests = await leaveRequestRepository.find({
        where: {
          leave_date: Between(startDate as string, endDate as string),
        },
        relations: ["user"],
      });

      // Create result structure
      const result = users.map((user) => {
        // Get user's attendance records
        const userAttendances = attendances.filter(
          (attendance) => attendance.user.id === user.id
        );

        // Get user's leave requests
        const userLeaves = leaveRequests.filter(
          (leave) => leave.user.id === user.id
        );

        // Create a map of dates with attendance data
        const dateMap: Record<
          string,
          {
            worked: boolean;
            hours?: number;
            leave?: string;
            isHoliday: boolean;
          }
        > = {};

        // Fill in attendance data
        userAttendances.forEach((attendance) => {
          dateMap[attendance.date] = {
            worked: true,
            hours: attendance.work_duration || 0,
            isHoliday: false,
          };
        });

        // Fill in leave data
        userLeaves.forEach((leave) => {
          dateMap[leave.leave_date] = {
            worked: false,
            leave: leave.leave_type,
            isHoliday: false,
          };
        });

        // Generate all dates in range to check for day offs
        const start = new Date(startDate as string);
        const end = new Date(endDate as string);
        const dateArray = [];

        for (
          let dt = new Date(start);
          dt <= end;
          dt.setDate(dt.getDate() + 1)
        ) {
          const dateStr = dt.toISOString().split("T")[0];
          const dayName = [
            "Sunday",
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday",
          ][dt.getDay()];

          // If not already in map and it's the user's day off
          if (!dateMap[dateStr] && dayName === user.day_off) {
            dateMap[dateStr] = {
              worked: false,
              isHoliday: true,
            };
          }

          dateArray.push(dateStr);
        }

        return {
          userId: user.id,
          userName: user.name,
          dayOff: user.day_off,
          dates: dateMap,
        };
      });

      res.status(200).json(result);
    } catch (error) {
      console.error("Get attendance by date range error:", error);
      res.status(500).json({
        message: "Server error while fetching attendance by date range",
      });
    }
  }

  public async getAverageWorkTime(req: Request, res: Response): Promise<void> {
    const attendanceRepository = AppDataSource.getRepository(Attendance);
    const userRepository = AppDataSource.getRepository(User);

    try {
      // Get all users
      const users = await userRepository.find({
        select: {
          id: true,
          name: true,
          role: true,
        },
      });

      const averageWorkTimeData = await Promise.all(
        users.map(async (user) => {
          // Get all attendance records with clock_out (completed workdays)
          const attendances = await attendanceRepository.find({
            where: {
              user: { id: user.id },
              clock_out: Not(IsNull()),
            },
          });

          // Calculate average work duration
          let totalWorkDuration = 0;
          attendances.forEach((attendance) => {
            totalWorkDuration += attendance.work_duration || 0;
          });

          const averageWorkDuration =
            attendances.length > 0
              ? Number((totalWorkDuration / attendances.length).toFixed(2))
              : 0;

          return {
            userId: user.id,
            userName: user.name,
            role: user.role,
            totalDays: attendances.length,
            totalHours: Number(totalWorkDuration.toFixed(2)),
            averageHoursPerDay: averageWorkDuration,
          };
        })
      );

      res.status(200).json(averageWorkTimeData);
    } catch (error) {
      console.error("Get average work time error:", error);
      res.status(500).json({
        message: "Server error while calculating average work time",
      });
    }
  }

  public async getMonthlyHoursByDateRange(
    req: Request,
    res: Response
  ): Promise<void> {
    const attendanceRepository = AppDataSource.getRepository(Attendance);
    const userRepository = AppDataSource.getRepository(User);

    try {
      // Get startDate and endDate from either query params or request body
      const startDate = req.query.startDate || req.body.startDate;
      const endDate = req.query.endDate || req.body.endDate;

      if (!startDate || !endDate) {
        res.status(400).json({ message: "startDate and endDate are required" });
        return;
      }

      // Validate date format
      if (
        !/^\d{4}-\d{2}-\d{2}$/.test(startDate as string) ||
        !/^\d{4}-\d{2}-\d{2}$/.test(endDate as string)
      ) {
        res.status(400).json({ message: "Dates must be in YYYY-MM-DD format" });
        return;
      }

      // Get all users with their roles
      const users = await userRepository.find({
        select: {
          id: true,
          name: true,
          role: true,
        },
      });

      // Get all attendance records within date range
      const attendances = await attendanceRepository.find({
        where: {
          date: Between(startDate as string, endDate as string),
        },
        relations: ["user"],
      });

      // Calculate statistics
      const totalEmployees = users.length;

      // Count employees by type
      const fullTimeEmployees = users.filter(
        (user) => user.role === "Employee" || user.role === "Manager"
      ).length;

      const partTimeEmployees = users.filter(
        (user) => user.role === "Part-time"
      ).length;

      // Calculate total late days
      const lateDays = attendances.filter(
        (attendance) => attendance.status === "Late"
      ).length;

      // Process data for each user
      const employeeData = users.map((user) => {
        // Get user's attendance records
        const userAttendances = attendances.filter(
          (attendance) => attendance.user.id === user.id
        );

        // Calculate total work hours for this user in the date range
        const totalWorkHours = userAttendances.reduce((total, attendance) => {
          return total + (attendance.work_duration || 0);
        }, 0);

        // Count late days for this user
        const userLateDays = userAttendances.filter(
          (attendance) => attendance.status === "Late"
        ).length;

        return {
          userId: user.id,
          name: user.name,
          totalWorkHours: Number(totalWorkHours.toFixed(2)),
          lateDays: userLateDays,
        };
      });

      // Calculate average working hours per employee
      const totalWorkHours = employeeData.reduce(
        (sum, employee) => sum + employee.totalWorkHours,
        0
      );
      const averageWorkHours =
        totalEmployees > 0
          ? Number((totalWorkHours / totalEmployees).toFixed(2))
          : 0;

      // Prepare response
      const response = {
        summary: {
          totalEmployees,
          averageWorkHours,
          employeeTypes: {
            fullTime: fullTimeEmployees,
            partTime: partTimeEmployees,
          },
          totalLateDays: lateDays,
        },
        employees: employeeData,
      };

      res.status(200).json(response);
    } catch (error) {
      console.error("Get monthly statistics error:", error);
      res.status(500).json({
        message: "Server error while calculating employee statistics",
      });
    }
  }
}
