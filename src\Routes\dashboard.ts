import { Router } from "express";
import { InventoryDashboardController } from "../Controllers/Dashboard/InventoryDashboard";
import { GoodsReceiptDashboardController } from "../Controllers/Dashboard/GoodsReceiptDashboard";
import { TransferDashboardController } from "../Controllers/Dashboard/TransferDashboard";
import { PurchaseOrderDashboardController } from "../Controllers/Dashboard/PurchaseOrderDashboard";
import { PerformanceDashboardController } from "../Controllers/Dashboard/PerformanceDashboard";

const router = Router();

// Initialize controllers
const inventoryController = new InventoryDashboardController();
const goodsReceiptController = new GoodsReceiptDashboardController();
const transferController = new TransferDashboardController();
const purchaseOrderController = new PurchaseOrderDashboardController();
const performanceController = new PerformanceDashboardController();

// Inventory Dashboard Routes
router.get("/inventory/by-branch", inventoryController.getStockByBranch);
router.get("/inventory/top-remaining-products", inventoryController.getTopRemainingProducts);
router.get("/inventory/value-by-group", inventoryController.getValueByGroup);


// Goods Receipt Dashboard Routes
router.get("/goods-receipt/volume-by-month", goodsReceiptController.getVolumeByMonth);
router.get("/goods-receipt/top-suppliers", goodsReceiptController.getTopSuppliers);
router.get("/goods-receipt/value-by-month", goodsReceiptController.getValueByMonth);

// Transfer Dashboard Routes
router.get("/transfer/volume-by-branch", transferController.getVolumeByBranch);
router.get("/transfer/top-issued-products", transferController.getTopIssuedProducts);
router.get("/transfer/flow-map", transferController.getFlowMap);

// Purchase Order Dashboard Routes
router.get("/purchase-orders/summary-by-month", purchaseOrderController.getSummaryByMonth);
router.get("/purchase-orders/top-products", purchaseOrderController.getTopProducts);
router.get("/purchase-orders/supplier-ratio", purchaseOrderController.getSupplierRatio);

// Performance Dashboard Routes
router.get("/performance/top-on-time-suppliers", performanceController.getTopOnTimeSuppliers);
router.get("/performance/slow-products", performanceController.getSlowProducts);
router.get("/performance/supplier-efficiency", performanceController.getSupplierEfficiency);

export default router;