import { Router } from "express";
import { StockTransferSlipDetailsController } from "../Controllers/StockTransferSlipDetails";

const router = Router();
const controller = new StockTransferSlipDetailsController();

router.get("/", controller.getAll.bind(controller));
router.get("/:id", controller.getById.bind(controller));
router.get("/sts/:stsId", controller.getBySTS.bind(controller));
router.post("/:stsId", controller.create.bind(controller));
router.post("/sts/:stsId/sto/:stoId", controller.createBySTO.bind(controller));
router.put("/:stsId/update-from-sto", controller.updateSTSDetailsFromSTO.bind(controller));
router.put("/:id", controller.update.bind(controller));
router.delete("/:id", controller.delete.bind(controller));
router.put("/:id/updateMultiple", controller.updateMultiple.bind(controller));
export default router;
