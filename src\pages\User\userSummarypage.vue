<template>
  <UserNavigation></UserNavigation>
  <div class="q-pa-md">
    <div class="row q-col-gutter-md">
      <!-- Left Container -->
      <div class="col-12 col-md-6">
        <div class="container q-pa-md">
          <div class="container-content">
            <div class="row attendance-cards">
              <AttendanceCard
                :total="fullTimeTotal"
                :present="fullTimePresent"
                :late="fullTimeLate"
                :sick-leave="fullTimeSickLeave"
                :personal-leave="fullTimePersonalLeave"
                employee-type="พนักงานประจำ"
                class="atten-card-left atten-card"
              />
              <AttendanceCard
                :total="partTimeTotal"
                :present="partTimePresent"
                :late="partTimeLate"
                :sick-leave="partTimeSickLeave"
                :personal-leave="partTimePersonalLeave"
                employee-type="พนักงานพาร์ทไทม์"
                class="atten-card-right atten-card"
              />
            </div>
            <DateRangeCalendar
              v-model="dateRange"
              title=""
              @reset="handleDateReset"
              @range-complete="navigateToAttendanceTable"
            />
          </div>
        </div>
      </div>

      <!-- Right Container -->
      <div class="col-12 col-md-6">
        <div class="container q-pa-md">
          <UserList />
        </div>
      </div>
    </div>

    <!-- Navigation button to dashboard -->
    <div class="row justify-center q-mt-lg">
      <q-btn
        to="/user/dashboard"
        unelevated
        icon="arrow_forward"
        label="ไปภาพรวม"
        class="dashboard-btn"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import UserNavigation from 'src/components/userNavigation.vue'
import AttendanceCard from 'src/pages/User/components/AttendanceCard.vue'
import DateRangeCalendar from 'src/pages/User/components/DateRangeCalendar.vue'
import UserList from 'src/pages/User/components/UserList.vue'
import { useAttendanceStore } from 'src/stores/attendance'
import { date } from 'quasar'

const router = useRouter()
const attendanceStore = useAttendanceStore()

// Date range picker ref
const dateRange = ref({
  from: date.formatDate(new Date(), 'YYYY/MM/DD'),
  to: date.formatDate(new Date(), 'YYYY/MM/DD'),
})

// Navigate to attendance table with date range params
const navigateToAttendanceTable = (range: { from: string; to: string }) => {
  router
    .push({
      name: 'attendance-table',
      query: {
        from: range.from,
        to: range.to,
      },
    })
    .catch((error) => {
      console.error('Navigation error:', error)
    })
}

const fullTimeTotal = ref(0)
const partTimeTotal = ref(0)

// For full-time employees
const fullTimePresent = ref(0)
const fullTimeLate = ref(0)
const fullTimeSickLeave = ref(0)
const fullTimePersonalLeave = ref(0)

// For part-time employees
const partTimePresent = ref(0)
const partTimeLate = ref(0)
const partTimeSickLeave = ref(0)
const partTimePersonalLeave = ref(0)

onMounted(async () => {
  const totals = await attendanceStore.fetchTotalByCategory()
  updateAttendanceValues(totals)
})

// Helper function to update all attendance values
const updateAttendanceValues = (totals: {
  fullTime?: {
    total?: number
    present?: number
    late?: number
    sickLeave?: number
    personalLeave?: number
  }
  partTime?: {
    total?: number
    present?: number
    late?: number
    sickLeave?: number
    personalLeave?: number
  }
}) => {
  // Full-time values
  fullTimeTotal.value = totals?.fullTime?.total || 0
  fullTimePresent.value = totals?.fullTime?.present || 0
  fullTimeLate.value = totals?.fullTime?.late || 0
  fullTimeSickLeave.value = totals?.fullTime?.sickLeave || 0
  fullTimePersonalLeave.value = totals?.fullTime?.personalLeave || 0

  // Part-time values
  partTimeTotal.value = totals?.partTime?.total || 0
  partTimePresent.value = totals?.partTime?.present || 0
  partTimeLate.value = totals?.partTime?.late || 0
  partTimeSickLeave.value = totals?.partTime?.sickLeave || 0
  partTimePersonalLeave.value = totals?.partTime?.personalLeave || 0
}

// Handle date reset event from calendar component
const handleDateReset = async () => {
  await attendanceStore.fetchTotalByCategory().then((totals) => {
    updateAttendanceValues(totals)
  })
}
</script>

<style scoped>
.container {
  background-color: white;
  border-radius: 10px;
  height: 100%;
  min-height: 500px;
}

.container-content {
  background-color: #f5f5f5;
  border-radius: 10px;
  height: 100%;
}

.calendar-container {
  margin-bottom: 16px;
  margin-left: 16px;
  margin-right: 16px;
}

.atten-card-left {
  margin: 16px 0 0 16px;
}

.atten-card-right {
  margin: 16px 16px 0 0;
}

.no-shadow :deep(.q-date) {
  box-shadow: none;
}

.atten-card {
  background-color: white;
  border-radius: 10px;
}

.type-emp {
  color: #294888;
}

p {
  margin: 0;
}

.dashboard-btn {
  background-color: #609fa3;
  color: white;
  border-radius: 10px;
  padding: 10px 20px;
}

.attendance-cards {
  justify-content: space-between;
}

@media (max-width: 1276px) {
  .attendance-cards {
    justify-content: center !important;
  }

  .atten-card-left,
  .atten-card-right {
    margin: 16px 8px;
  }
}

@media (min-width: 1481px) and (max-width: 1900px) {
  .attendance-cards {
    justify-content: center !important;
  }
  .atten-card-left,
  .atten-card-right {
    margin: 16px 32px;
  }
}
</style>
